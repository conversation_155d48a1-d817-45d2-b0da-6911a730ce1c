import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import SEO from '../components/seo';
import '../styles/app.scss';

const WeRockCare = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt respitesec padding-less-under">
                <div className="container">
                    <div className="openplft">
                        <img
                            src={
                                post.acf.weRockCareImage?.localFile?.childImageSharp?.gatsbyImageData?.images?.fallback?.src ||
                                ''
                            }
                            alt="respite"
                        />
                    </div>

                    <div className="openprgt withmarbtm">
                        <h2 className="bluetxt">WHY WE ROCK CARE?</h2>
                        <p
                            className="lastitem"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.weRockCareContent,
                            }}
                        />
                    </div>

                    <div className="werockcareheal">
                        {/* <h5 className="bluetxt">Book we rock care</h5> */}
                        {/* <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.book_we_rock_care_link,
                            }}
                        /> */}
                    </div>

                    <div className="werockcareheal">
                        <h5 className="bluetxt">Book we rock care</h5>
                        <iframe
                            src="https://wordpress-1396913-5383437.cloudwaysapps.com/healcode-werock.php"
                            title="appointment"
                            className="healcode_widgets"
                        />
                    </div>
                </div>
            </section>

            <section
                className="page-section bg-secondary text-white openphours"
                id="pricingsec"
            >
                <div className="container smallestwdt flexwrap">
                    <div className="twothirdcol flexbox toup">
                        <h2 className="yellowtxt">Pricing</h2>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.whyWeRockCarePricing,
                            }}
                        />
                        <h5 className="yellowtxt addinfo">
                            ADDITIONAL INFORMATION
                        </h5>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.whyWeRockCareAdditionalInformations,
                            }}
                        />
                    </div>

                    <div className="onethirdcol flexbox todwn">
                        {post.acf.whyWeRockCareFormsList.map(
                            (formItem, i) => (
                                <a
                                    href={formItem.pdfFormFile.url.sourceUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="wrtsbtn yellowbtn fullbtn"
                                    key={i}
                                    dangerouslySetInnerHTML={{
                                        __html: formItem.pdfFormName,
                                    }}
                                />
                            )
                        )}
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default WeRockCare;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            seo {
                title
                metaDesc
            }
            acf {
                weRockCareTitle
                weRockCareContent
                weRockCareImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: FULL_WIDTH
                                placeholder: BLURRED
                            )
                        }
                    }
                }
                bookWeRockCareLink
                whyWeRockCareFormsList {
                    pdfFormName
                    pdfFormFile {
                        url {
                            sourceUrl
                        }
                    }
                }
                whyWeRockCarePricing
                whyWeRockCareAdditionalInformations
            }
        }
    }
`;
