import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import SEO from '../components/seo';
import '../styles/app.scss';

const WeRockCare = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt respitesec padding-less-under">
                <div className="container">
                    <div className="openplft">
                        <img
                            src={
                                post.acf.we_rock_care_image?.localFile?.childImageSharp?.gatsbyImageData?.images?.fallback?.src ||
                                ''
                            }
                            alt="respite"
                        />
                    </div>

                    <div className="openprgt withmarbtm">
                        <h2 className="bluetxt">WHY WE ROCK CARE?</h2>
                        <p
                            className="lastitem"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.we_rock_care_content,
                            }}
                        />
                    </div>

                    <div className="werockcareheal">
                        {/* <h5 className="bluetxt">Book we rock care</h5> */}
                        {/* <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.book_we_rock_care_link,
                            }}
                        /> */}
                    </div>

                    <div className="werockcareheal">
                        <h5 className="bluetxt">Book we rock care</h5>
                        <iframe
                            src="https://wordpress-1396913-5383437.cloudwaysapps.com/healcode-werock.php"
                            title="appointment"
                            className="healcode_widgets"
                        />
                    </div>
                </div>
            </section>

            <section
                className="page-section bg-secondary text-white openphours"
                id="pricingsec"
            >
                <div className="container smallestwdt flexwrap">
                    <div className="twothirdcol flexbox toup">
                        <h2 className="yellowtxt">Pricing</h2>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.why_we_rock_care_pricing,
                            }}
                        />
                        <h5 className="yellowtxt addinfo">
                            ADDITIONAL INFORMATION
                        </h5>
                        <div
                            dangerouslySetInnerHTML={{
                                __html:
                                    post.acf
                                        .why_we_rock_care_additional_informations,
                            }}
                        />
                    </div>

                    <div className="onethirdcol flexbox todwn">
                        {post.acf.why_we_rock_care_forms_list.map(
                            (formItem, i) => (
                                <a
                                    href={formItem.pdf_form_file.url.source_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="wrtsbtn yellowbtn fullbtn"
                                    key={i}
                                    dangerouslySetInnerHTML={{
                                        __html: formItem.pdf_form_name,
                                    }}
                                />
                            )
                        )}
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default WeRockCare;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            seo {
                title
                metaDesc
            }
            acf {
                we_rock_care_title
                we_rock_care_content
                we_rock_care_image {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: FULL_WIDTH
                                placeholder: BLURRED
                            )
                        }
                    }
                }
                book_we_rock_care_link
                why_we_rock_care_forms_list {
                    pdf_form_name
                    pdf_form_file {
                        url {
                            source_url
                        }
                    }
                }
                why_we_rock_care_pricing
                why_we_rock_care_additional_informations
            }
        }
    }
`;
