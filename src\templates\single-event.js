import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import '../styles/app.scss';

const SingleEvent = ({ data }) => {
    const { wpEventsPostType: eventData } = data;

    const seoTitle = eventData.seo?.title || eventData.title;
    const seoDescription = eventData.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Event" />

            <section className="page-section">
                <div className="container blogwrapper blogarticle">
                    <div className="bloglft">
                        <div className="blogimg">
                            {eventData.featuredImage?.node?.localFile?.childImageSharp?.gatsbyImageData && (
                                <img
                                    src={eventData.featuredImage.node.localFile.childImageSharp.gatsbyImageData.images.fallback.src}
                                    alt={eventData.title || 'Event Image'}
                                />
                            )}
                        </div>
                        <div className="bloxexc">
                            <h2
                                dangerouslySetInnerHTML={{
                                    __html: eventData.title,
                                }}
                            />
                            {eventData.acf?.eventDate && (
                                <h5
                                    dangerouslySetInnerHTML={{
                                        __html: eventData.acf.eventDate,
                                    }}
                                />
                            )}
                        </div>
                        <img
                            className="blogline"
                            src={LineFull}
                            alt="linefull"
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: eventData.content,
                            }}
                        />
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export const pageQuery = graphql`
    query SingleEvent($id: String!) {
        wpEventsPostType(id: { eq: $id }) {
            title
            content
            seo {
                title
                metaDesc
            }
            acf {
                eventDate
            }
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 800
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
        }
    }
`;

export default SingleEvent;
