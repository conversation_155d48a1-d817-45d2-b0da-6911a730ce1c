import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import Footer from '../components/Footer';
import SEO from '../components/seo';
import '../styles/app.scss';

const WeRecommend = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section className="page-section centersec smallestwdt nopaddbottsec">
                <div className="container">
                    <h2
                        className="bluetxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.weRecommendHeading,
                        }}
                    />
                    <p
                        className="lastitem biggertxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.weRecommendDescription,
                        }}
                    />

                    <img
                        className="schoolsline"
                        src={LineFull}
                        alt="linefull"
                    />
                </div>

                <div className="werecommwrap">
                    {post.acf.weRecommendLogos.map((logo, i) => (
                        <div className="recommimg" key={i}>
                            <a
                                href={logo.wecommendUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <img
                                    src={
                                        logo.recommendLogo?.localFile?.childImageSharp
                                            ?.gatsbyImageData?.images?.fallback?.src || ''
                                    }
                                    alt="werec"
                                />
                            </a>
                        </div>
                    ))}
                </div>
            </section>
            <Footer />
        </>
    );
};

export default WeRecommend;


export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            ... on WpACFWeRecommend {
                weRecommendHeading
                weRecommendDescription
                weRecommendLogos {
                    recommendLogo {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: FULL_WIDTH
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                    wecommendUrl
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;