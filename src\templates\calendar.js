/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState } from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import Footer from '../components/Footer';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import '../styles/app.scss';

const Calendar = ({ data }) => {
    const { wpPage: post } = data;

    const [limit, setLimit] = useState(3);

    function loadImages() {
        return post.acf?.calendar_images
            ?.slice(0, limit)
            .map((month, i) => (
                <img
                    src={month.localFile?.childImageSharp?.fluid?.src || ''}
                    alt={`Calendar month ${i + 1}`}
                    key={i}
                />
            ));
    }

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section centersec">
                <div className="container smallestwdt wrtscalendarwrap">
                    {loadImages()}
                </div>

                <div>
                    <img
                        className="purposeline"
                        src={LineFull}
                        alt="Decorative line"
                    />
                    <a
                        className="wrtsbtn yellowbtn pointer"
                        onClick={() => setLimit(limit + 3)}
                    >
                        LOAD MORE
                    </a>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default Calendar;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            acf {
                calendar_images {
                    localFile {
                        childImageSharp {
                            fluid {
                                src
                            }
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;