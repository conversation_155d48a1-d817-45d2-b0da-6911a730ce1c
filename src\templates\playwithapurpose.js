import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import PurposeList from '../components/pages/PurposesList';
import PosterPurposeDownload from '../components/pages/PosterPurposeDownload';
import SEO from '../components/seo';
import '../styles/app.scss';

const PlayWithPurpose = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <PurposeList purposeList={post.acf} />
            <PosterPurposeDownload posterData={post.acf} />
            <Footer />
        </>
    );
};

export default PlayWithPurpose;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            ... on WpACFPlayWithAPurpose {
                playWithAPurposeDescription
                purposeList {
                    purposeText
                    purposeName
                    purposeStartImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 600
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                    purposeKidImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 600
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
                purposePosterDescription
                purposePosterTitle
                purposePosterImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 600
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;
