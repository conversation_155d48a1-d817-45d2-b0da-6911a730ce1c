import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import PurposeList from '../components/pages/PurposesList';
import PosterPurposeDownload from '../components/pages/PosterPurposeDownload';
import SEO from '../components/seo';
import '../styles/app.scss';

const PlayWithPurpose = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <PurposeList purposeList={post.acf} />
            <PosterPurposeDownload posterData={post.acf} />
            <Footer />
        </>
    );
};

export default PlayWithPurpose;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            acf {
                play_with_a_purpose_description
                purpose_list {
                    purpose_text
                    purpose_name
                    purpose_start_image {
                        localFile {
                            childImageSharp {
                                fluid {
                                    srcWebp
                                    srcSetWebp
                                    srcSet
                                    src
                                }
                            }
                        }
                    }
                    purpose_kid_image {
                        localFile {
                            childImageSharp {
                                fluid {
                                    srcSetWebp
                                    srcWebp
                                    srcSet
                                    src
                                }
                            }
                        }
                    }
                }
                purpose_poster_description
                purpose_poster_title
                purpose_poster_image {
                    localFile {
                        childImageSharp {
                            fluid {
                                srcSetWebp
                                srcWebp
                                srcSet
                                src
                            }
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;
