import React from 'react';
import { Link, graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';

const Schedule = () => {
    const data = useStaticQuery(graphql`
        query {
            sidebarImage: file(relativePath: { eq: "sidebar1.png" }) {
                childImageSharp {
                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                }
            }
        }
    `);

    const sidebarImage = getImage(data.sidebarImage);

    return (
        <div className="sidewidget widgetnopadd">
            <div className="sidebarimg">
                <GatsbyImage image={sidebarImage} alt="sidebar" />
                <Link to="/schedule" className="wrtsbtn yellowbtn inlinebtn">
                    VIEW OUR SCHEDULE
                </Link>
            </div>
        </div>
    );
};

export default Schedule;
