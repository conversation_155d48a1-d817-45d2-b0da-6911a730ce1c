import React from 'react';
import { graphql } from 'gatsby';
import SEO from '../components/seo';
import Footer from '../components/Footer';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import '../styles/app.scss';

const GiftCards = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section className="page-section-giftcards giftcardsection">
                <div
                    className="container"
                    dangerouslySetInnerHTML={{
                        __html: post.content,
                    }}
                />
                <iframe
                    src="https://werockthespectrummilwaukee.wrtsfranchise.com/gift-cards"
                    title="register"
                    className="healcode_widgets"
                />
            </section>
            <div className="popup-wrap gift_popup">
                <div className="popup">
                    <span className="close_popup">×</span>
                    <iframe
                        src="https://werockthespectrummilwaukee.wrtsfranchise.com/giftcardregister.php"
                        title="appointment"
                        className="healcode_widgets"
                    />
                </div>
            </div>
            <Footer />
        </>
    );
};

export default GiftCards;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            seo {
                title
                metaDesc
            }
        }
    }
`;