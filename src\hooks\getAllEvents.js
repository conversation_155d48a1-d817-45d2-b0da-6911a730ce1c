import { useStaticQuery, graphql } from 'gatsby';
import { Link } from 'gatsby';

export const useAllEvents = () => {
    const data = useStaticQuery(graphql`
        query GET_ALL_EVENTS {
            allWpEventsPostType(sort: {date: DESC}) {
                nodes {
                    id
                    title
                    slug
                    date
                    excerpt
                    featuredImage {
                        node {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(
                                        layout: CONSTRAINED
                                        width: 800
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.allWpEventsPostType?.nodes) {
        console.error('No events found');
        return [];
    }

    return data.allWpEventsPostType.nodes.map(event => ({
        id: event.id,
        title: event.title,
        slug: event.slug,
        date: event.date,
        excerpt: event.excerpt,
        featuredImage: event.featuredImage?.node?.localFile?.childImageSharp
            ? event.featuredImage.node.localFile.childImageSharp.gatsbyImageData
            : null,
        link: <Link to={`/events/${event.slug}`}>Read More</Link>,
    }));
};
