import { useStaticQuery, graphql } from 'gatsby';

export const useTestimonials = () => {
    const data = useStaticQuery(graphql`
        query GET_HOME_TESTIMONIALS_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                aCFHomeTestimonials {
                    whatParentsSayList {
                        parentsTestimonial
                        parentsName
                    }
                    whatParentsSayImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.aCFHomeTestimonials) {
        console.error('Testimonials data not found');
        return {
            testimonialsList: [],
            testimonialsImage: null,
        };
    }

    const { whatParentsSayList, whatParentsSayImage } = data.wpPage.aCFHomeTestimonials;

    return {
        testimonialsList: whatParentsSayList || [],
        testimonialsImage: whatParentsSayImage?.localFile?.childImageSharp
            ? whatParentsSayImage.localFile.childImageSharp.gatsbyImageData
            : null,
    };
};
