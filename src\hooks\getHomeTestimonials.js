import { useStaticQuery, graphql } from 'gatsby';

export const useTestimonials = () => {
    const data = useStaticQuery(graphql`
        query GET_HOME_TESTIMONIALS_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    what_parents_say_list {
                        parents_testimonial
                        parents_name
                    }
                    what_parents_say_image {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Testimonials data not found');
        return {
            testimonialsList: [],
            testimonialsImage: null,
        };
    }

    const { what_parents_say_list, what_parents_say_image } = data.wpPage.acf;

    return {
        testimonialsList: what_parents_say_list || [],
        testimonialsImage: what_parents_say_image?.localFile?.childImageSharp
            ? what_parents_say_image.localFile.childImageSharp.gatsbyImageData
            : null,
    };
};
