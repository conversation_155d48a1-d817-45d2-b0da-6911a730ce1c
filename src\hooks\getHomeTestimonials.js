import { useStaticQuery, graphql } from 'gatsby';

export const useTestimonials = () => {
    const data = useStaticQuery(graphql`
        query GET_HOME_TESTIMONIALS_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                title
                content
            }
        }
    `);

    if (!data?.wpPage) {
        console.error('Testimonials data not found');
        return {
            testimonialsList: [],
            testimonialsImage: null,
        };
    }

    // Temporary fallback values until ACF is configured
    const testimonialsList = [
        {
            parentsTestimonial: 'ACF fields are being configured. Testimonials will be restored once WordPress ACF integration is complete.',
            parentsName: 'System Message'
        }
    ];
    const testimonialsImage = null;

    return {
        testimonialsList,
        testimonialsImage,
    };
};
