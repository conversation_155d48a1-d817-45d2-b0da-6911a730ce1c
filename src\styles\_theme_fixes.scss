.display_block {
  display: block !important;
}

.tiktok {
    width: 100%;
    max-width: 50px;
    height: auto;

}
.cart-div {
    display: inline-block;
}

.cart-div a.healcode-link.healcode-cart-text-link::before {
    background-image: url('https://werockthespectrumsugarland.wrtsfranchise.com/wp-content/uploads/2024/03/cart-icon1.svg');
    background-size: 51px 51px;
    display: inline-block;
    width: 51px;
    height: 51px;
    content: "";
}

@media only screen and (max-width: 480px) {
    .cart-div a.healcode-link.healcode-cart-text-link:before {
            background-image: url('https://werockthespectrumsugarland.wrtsfranchise.com/wp-content/uploads/2024/03/cart-icon1.svg');
            background-size: 36px 35px;
            display: inline-block;
            width: 35px;
            height: 35px;
            content: "";
        }
}

// global location
.parent-iframe {
  position: relative;
  width: 100%;
  // overflow: hidden;
  // padding-top: 100%; 
}


.wrts-global-locations {
 
  min-width: 100%;
  border: none;
}


// global loc done
.new-abt-gall {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 40px;
    padding-bottom: 50px;
    max-width: 1300px;
    margin: auto;
    padding-left: 10px;
    padding-right: 10px;

}
.new-abt-gall div img {
  width: 100%;
  height: auto;
}
.new-abt-gall div {
  width: 30%;
  max-width: 600px;
}
.homeeventbox .homeevdate {
display: none;
}
.toup ul.starlistspacing li span {
  font-family: "gothamblack";
}
#aboutsliderdiv .slider_owners img {
max-width: 600px;
margin: auto;
}
.container.blogwrapper.hidden-dates .bloxexc h5 {
  display: none;
}
.bluetxt.centerme {
 text-align: center;
 margin-bottom: 70px;
}
.pricingtxt {
  font-size: 20px !important;
  margin-bottom: 50px;
  font-weight: bold !important;
}
#openpbasic {
  margin-top: 50px;
}
.bluetxt.conthours a {
  text-decoration: underline !important;
}
.headertop h6 .headerfblink {
  color: #faed00;
  vertical-align: top;
}
.images-boxes {
  max-width: 1230px;
  margin-top: 50px;
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  row-gap: 20px;
  column-gap: 20px;
  flex-wrap: wrap;
}
.images-boxes-two {
  max-width: 1230px;
  margin-top: 50px;
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  row-gap: 20px;
  column-gap: 20px;
  flex-wrap: wrap;
}
@media only screen and (min-width: 999px) {
  .toup h5 {
    font-size: 48px;
    line-height: 66px;
    margin-bottom: 20px;
  }
  .flexbox.toup {
    padding-right: 20px;
  }
}
.toup ul.starlistspacing li,
.toup ul.new-rock-ul li {
  font-family: "gothambook";
}
.toup ul.new-rock-ul li span {
  font-family: "gothamblack";
}
.padding-less-under {
  padding-bottom: 40px !important;
}
.new-title-op {
  text-align: center;
  margin-bottom: 10px;
  font-family: "hogfish_demoregular";
}
.flyer-cominsoon {
  padding: 20px;
  margin-top: 30px;
  margin-bottom: 30px;
}
.fullblue h2 {
  color: #146fb8;
  max-width: 1000px;
  text-align: center;
  margin: 50px auto;
}
.sign-box-up {
  margin-bottom: 35px;
  margin-top: 35px;
  padding: 20px;
}
.footaddress {
  padding: 0 20px !important;
}
.footaddress h2 {
  font-size: 42px;
  line-height: 56px;
}
.whylistrgt .startxt p {
  margin-left: 30px;
  margin-top: 10px;
}
.signup-letter-new {
  font-family: "hogfish_demoregular";
  text-transform: uppercase;
  color: #146fb8 !important;
  padding: 15px 25px;
  font-size: 16px;
  background: #faed00;
  letter-spacing: 1px;
  margin: 20px 0 10px;
  border-radius: 50px;
  font-weight: normal !important;
}
.signup-letter-new:hover {
  color: rgb(255, 255, 255) !important;
  background: rgb(50, 137, 207);
  text-decoration: none;
}
.sign-box-up-par {
  margin-bottom: 30px;
}
.sign-box-up-par span {
  line-height: 1.1;
  font-family: "hogfish_demoregular";
  margin-bottom: 20px;
  font-weight: normal !important;
  letter-spacing: 1px;
  font-size: 36px;
}
.display_none {
  display: none;
}
.googlecal,
.googlecaltwo {
  width: 100%;
  max-width: 800px;
  height: 100%;
  height: 600px;
}
.googlecal {
  margin-top: 30px;
}

// TABS
.googlecal.loc-iframe {
        height: 27048px;
    border: none;
    max-width: 1284px;
        width: 1284px;
        margin-right: -20px;
}
.googlecal.loc-iframe.usa {

height: 27048px;
}
.googlecal.loc-iframe.usa-soon {
 
    height: 11848px;
}
.googlecal.loc-iframe.int {
 
    height: 3800px;
}
.googlecal.loc-iframe.int-soon {

    height: 3300px;
}

.tabcontent.location_iframbox {
    max-width: 1284px;
        overflow: hidden;
}
@media only screen and (max-width: 1284px) {
    .googlecal.loc-iframe.usa {
        height: 22500px;
    }
        .googlecal.loc-iframe.usa-soon {
           
            height: 9300px;
        }
    
        .googlecal.loc-iframe.int {
        
            height: 3150px;
        }
    
        .googlecal.loc-iframe.int-soon {
       
            height: 2500px;
        }
}
@media only screen and (max-width: 1024px) {
    .googlecal.loc-iframe.usa {
        height: 21000px;
    }
        .googlecal.loc-iframe.usa-soon {
            
            height: 9200px;
        }
    
        .googlecal.loc-iframe.int {
            
            height: 3100px;
        }
    
        .googlecal.loc-iframe.int-soon {
         
            height: 2400px;
        }
}
@media only screen and (max-width: 991px) {
    .googlecal.loc-iframe.usa {
        height: 19000px;
    }
        .googlecal.loc-iframe.usa-soon {
          
            height: 8200px;
        }
    
        .googlecal.loc-iframe.int {
           
            height: 2650px;
        }
    
        .googlecal.loc-iframe.int-soon {
         
            height: 2150px;
        }
}
@media only screen and (max-width: 767.5px) {
    .googlecal.loc-iframe.usa {
        height: 26000px;
    }
        .googlecal.loc-iframe.usa-soon {
          
            height: 13700px;
        }
    
        .googlecal.loc-iframe.int {
           
            height: 3500px;
        }
    
        .googlecal.loc-iframe.int-soon {
          
            height: 3450px;
        }
}
@media only screen and (max-width: 480px) {
.googlecal.loc-iframe.usa {
        height: 48000px;
    }
        .googlecal.loc-iframe.usa-soon {
           
            height: 26300px;
        }
    
        .googlecal.loc-iframe.int {
            
            height: 6650px;
        }
    
        .googlecal.loc-iframe.int-soon {
           
            height: 6100px;
        }
}
// TABS END

.hero_homepage_video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.ReactModal__Overlay.ReactModal__Overlay--after-open {
  z-index: 111;
}
.popup-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1111;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: all 0.5s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
}
.popup-wrap.show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.popup {
  position: relative;
  max-width: 65vw;
  width: 100%;
  background: #fff;
  padding: 40px;
}
.gift_popup .popup {
  max-width: 400px;
}
.close_popup {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  font-size: 40px;
  color: #333;
}
.carousel {
  position: relative;
  .carousel-indicators {
    text-align: center;
    display: flex;
    bottom: 45px;
    position: absolute;
    @media (max-width: 991px) {
      bottom: 15px;
    }
    li {
      display: inline-block;
      margin-right: 3px;
      margin-left: 3px;
      &.slick-current {
        background: #ffffff !important;
      }
      button {
        display: none;
      }
    }
  }
}

.carousel-indicators {
  &.indicators_blog {
    @media (max-width: 991px) {
      bottom: -55px;
    }
  }
  .slick-track {
    width: 100% !important;
  }
  .slick-list {
    .slick-slide {
      width: auto !important;
      &.slick-current {
        li {
          background: #ffffff !important;
        }
      }
      li {
        width: 11px !important;
        height: 11px !important;
      }
    }
  }
}

.margin-0 {
  margin: 0;
}

.margin-0-auto {
  margin: 0 auto;
  display: block;
}

.text_center {
  text-align: center;
}

.mt-15 {
  margin-top: 15px;
}

.width_100 {
  width: 100%;
}

.mb-4p {
  margin-bottom: 4%;
}
.mb-6p {
  margin-bottom: 6%;
}

.border_0 {
  border: 0;
}

.quoteimg {
  max-height: 334px;
  @media (max-width: 991px) {
    height: 118px !important;
  }
}

.pointer {
  cursor: pointer;
}

.card {
  border: 0;
}

.bloglftwrap {
  .bloglft {
    display: block;
    flex: 0;
  }
  .gatsby-image-wrapper {
    overflow: visible !important;
    height: 100%;
  }

  .blogimg {
    height: 300px;
    position: relative;
  }
}

.healcode_widgets {
  overflow: hidden;
}
// .explorespacediv {
//   display: none;
// }
.nomartopimg {
  margin-top: 0;
}
.pricingbox {
  margin-bottom: 60px;
}
.privplaydsec {
  margin-bottom: 20px;
  padding-bottom: 3%;
  overflow: hidden;
}
.privplaydsec .one_half {
  text-align: left;
}
.privplaydsec .one_half ul li {
  font-size: 16px;
  line-height: 24px;
}
.one_half {
  width: 50%;
  float: left;
}
// .staffsec {
//   display: none;
// }
.staffmember {
    margin-bottom: 15px;
}
.privplaydsec h5.bluetxt {
  text-decoration: underline;
}
.bdaybook {
  max-width: 500px;
  margin: 0 auto;
}
.smallerulli li {
  font-size: 16px;
  line-height: 24px;
}
.openprgtcodes {
  width: 46%;
  float: right;
}
.soonplaceholder {
  text-align: center;
  width: 100%;
}
.lftoplayheal {
  padding-right: 50px;
}
.werockcareheal {
  clear: both;
  width: 54%;
  margin: 0 auto;
  text-align: center;
}
.withmarbtm {
  margin-bottom: 40px;
}
.oplayhealwrap {
  text-align: center;
  max-width: 600px;
  margin: 20px auto;
}
.headertop a.hourslinka {
  color: #faed00;
  vertical-align: top;
}
.startxt.starlistspacing li a,
#contactsec h5 a {
  font-family: "hogfish_demoregular";
  text-decoration: none;
}
/**/
.giftbanner {
  width: 100%;
  margin-top: -80px;
}
.cardsworksec {
  background: url(https://werockthespectrumsanantonionorth.wrtsfranchise.com/wp-content/uploads/2021/01/akidsbg.png)
      center center repeat,
    #006fb9;
  float: left;
  padding: 50px;
  width: 100%;
}
.cardsworksec .one_half:first-of-type {
  padding-right: 3%;
}
.cardsworksec p {
  color: #ffffff;
}
.cardsworksec h2 {
  color: #faed00;
  text-align: center;
}
.cardinfo,
.giftccodeds {
  max-width: 1290px;
  margin: 0 auto;
}
a.healcode-link {
  color: red !important;
}
.gifcarreg {
  width: 100%;
  float: left;
  margin-top: 50px;
}
.gifcarreg .one_half {
  float: none;
  margin: 0 auto;
}
.giftcardsection .container {
  max-width: 100%;
  padding: 0;
}
.imgboymob {
  display: none;
}
.homeeventbox:only-child {
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .new-abt-gall div {
    width: 100%;
  }
  .blockipad {
    display: block;
  }
}

.globe.gatsby-image-wrapper {
  @media (max-width: 991px) {
    width: 100%;
    img {
      width: 100%;
      max-width: 100%;
    }
  }
}

@media (max-width: 991px) {
  .navitems ul li {
    a {
      display: block;
      .caret {
        cursor: pointer;
        pointer-events: none;
        margin-top: 7px;
      }
    }
  }
  .openprgt {
    text-align: center;
  }
  .openprgtcodes {
    float: none;
    display: block;
    margin: 0 auto;
    overflow: hidden;
  }
}

.contactform_wrap {
  max-width: 1260px;
  margin: 0 auto;
  iframe {
    border: 0;
    width: 100%;
    min-height: 560px;
  }
}

.blog_home_image {
  max-height: 358px;
}

// header logo
.navbar-brand {
  position: relative;
  .gatsby-image-wrapper {
    @media (max-width: 1080px) {
      margin: 0 auto;
    }
  }
  .gatsby-image-wrapper,
  img {
    width: 261px;
    height: auto;
  }
}

@media (max-width: 991px) {
  .homeglobergt img {
    margin-top: 0;
  }
  .toup {
    text-align: left !important;
  }
  .flexwrap {
    row-gap: 20px;
  }
}

@media (max-width: 768px) {
    .tiktok {
            width: 100%;
            max-width: 35px;
            height: auto;
    
        }
  .one_half {
    width: 100%;
  }
  .privplaydsec .one_half:first-of-type {
    margin-bottom: 30px;
  }
  .lftoplayheal {
    padding-right: 0;
  }
  .werockcareheal {
    width: 100%;
  }
  /**/
  .giftbanner {
    margin-top: -45px;
  }
  .cardsworksec .one_half:first-of-type {
    padding-right: 0;
  }
  .imgboymob {
    display: block;
    margin-top: 50px;
    float: left;
  }
  .imgboymob img {
    width: 90%;
    margin: 0 auto;
    display: block;
  }
}

@media (max-width: 600px) {
  .openprgtcodes {
    width: 100%;
  }
}

.accordion > .card .card-header {
  .accordion-trigger {
    padding-right: 55px;
  }
}

.bloxexc {
  .postName {
    text-decoration: none;
    h2 {
    }
  }
}

.purposeboxes p {
  text-align: left;
}

// why we rock plist
.whylistlft,
.whylistrgt {
  .plist {
    p {
      font-size: 24px;
      line-height: 36px;
      font-family: "gothambook";
      font-weight: normal;
    }
  }
}

.img_full_width {
  display: block;
  width: 100%;
}
