{"name": "gatsby-starter-default", "private": true, "description": "A simple starter to get up and developing quickly with Gatsby", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@glidejs/glide": "^3.4.1", "axios": "^0.19.2", "dotenv": "^16.0.3", "gatsby": "^5.0.0", "gatsby-plugin-image": "^3.0.0", "gatsby-plugin-manifest": "^5.0.0", "gatsby-plugin-netlify": "^5.0.0", "gatsby-plugin-offline": "^6.0.0", "gatsby-plugin-purgecss": "^6.0.0", "gatsby-plugin-react-helmet": "^6.0.0", "gatsby-plugin-sass": "^6.0.0", "gatsby-plugin-sharp": "^5.0.0", "gatsby-source-filesystem": "^5.0.0", "gatsby-source-wordpress": "^7.0.0", "gatsby-transformer-sharp": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0"}, "devDependencies": {"prettier": "^3.0.0"}, "keywords": ["gatsby"], "license": "MIT", "scripts": {"build": "gatsby build", "develop": "gatsby develop", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "start": "npm run develop", "serve": "gatsby serve", "clean": "gatsby clean"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}}