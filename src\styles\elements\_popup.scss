.popup_wrapper {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 100;
	background-color: rgba(0, 0, 0, 0.5);
	display: block;
	overflow: hidden;
	display: none;
	&.is_active {
		display: block;
	}
	.popup_content {
		max-width: 500px;
		margin: 0 auto;
		background-color: white;
		padding: 30px;
		padding-right: 0;
		// overflow
		height: calc(100vh - 30px);
		margin-top: 15px;
		position: relative;
	}
	.popup_close {
		color: #333;
		position: absolute;
		background-color: transparent;
		border: 0;
		position: absolute;
		top: 0;
		right: 0;
	}
	iframe {
		border: 0;
		width: 100%;
		height: 100%;
		@media (max-width: 991px) {
			height: 90vh;
		}
	}
}

//

.healcode_widgets {
	width: 100%;
	height: auto;
	min-height: 500px;
	border: 0;
}
