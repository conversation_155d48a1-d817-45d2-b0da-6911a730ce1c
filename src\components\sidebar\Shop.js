import React from 'react';
import { graphql, useStaticQuery } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';

const Shop = () => {
    const data = useStaticQuery(graphql`
        query {
            sidebarImage: file(relativePath: { eq: "sidebar2.png" }) {
                childImageSharp {
                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                }
            }
        }
    `);

    const sidebarImage = getImage(data.sidebarImage);

    return (
        <div className="sidewidget widgetnopadd">
            <div className="sidebarimg">
                <GatsbyImage image={sidebarImage} alt="Shop promotional banner" />
                <a
                    href="https://shop.werockthespectrumkidsgym.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="wrtsbtn yellowbtn inlinebtn"
                >
                    CHECK OUT OUR ROCK SHOP
                </a>
            </div>
        </div>
    );
};

export default Shop;
