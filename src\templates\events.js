import React from 'react';
import { Link } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import Footer from '../components/Footer';
import LineFull from '../images/linefull.jpg';
import { useAllEvents } from '../hooks/getAllEvents';
import '../styles/app.scss';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import SEO from '../components/seo';
import Shop from '../components/sidebar/Shop';

const Events = () => {
    const eventsData = useAllEvents();

    return (
        <>
            <SEO title="Events" />
            <HeaderMain />
            <HeroSingle pageTitle="Events" />

            <section className="page-section">
                {/* <h2 className='bluetxt centerme'>Space Available For Rent</h2> */}
                <div className="container blogwrapper hidden-dates">
                    <div className="bloglftwrap">
                        {eventsData.map((event, i) => (
                            <div className="bloglft" key={i}>
                                <div className="blogimg">
                                    {event.featuredImage?.node?.localFile?.childImageSharp?.fluid?.src && (
                                        <img
                                            src={event.featuredImage.node.localFile.childImageSharp.fluid.src}
                                            alt={event.title || 'Event Image'}
                                        />
                                    )}

                                </div>
                                <div className="bloxexc">
                                    <h2
                                        dangerouslySetInnerHTML={{
                                            __html: event.title,
                                        }}
                                    />
                                    <h5>
                                        {new Intl.DateTimeFormat('en-US', {
                                            month: 'long',
                                            day: 'numeric',
                                            year: 'numeric',
                                        }).format(new Date(event.date))}
                                    </h5>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: event.excerpt,
                                        }}
                                    />
                                    <Link to={`/${event.slug}`}>Read More</Link>
                                </div>
                                <img
                                    className="blogline"
                                    src={LineFull}
                                    alt="linefull"
                                />
                            </div>
                        ))}
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default Events;
