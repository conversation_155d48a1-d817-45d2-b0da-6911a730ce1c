import React from 'react';
import { graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import BirthdayPartiesSlider from '../components/pages/BirthdayPartiesSlider';
import LineFull from '../images/linefull.jpg';
import PriceLine from '../images/pricline.png';
import GreenLogo from '../images/greenlogo.jpg';
import SEO from '../components/seo';
import '../styles/app.scss';

const BirthDay = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title?.replace('&#039;', "'") || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt" id="bookparty">
                <div className="container">
                    <h2 className="bluetxt">BOOK YOUR BIRTHDAY PARTY!</h2>

                    <div className="bookawrap">
                        <a
                            // href={`tel:${post.acf.party_call_us_number}`}
                            href="#packages-options"
                            className="wrtsbtn yellowbtn fullbtn"
                        >
                            {/* CALL US TODAY: {post.acf.party_call_us_number} */}
                            SEE OUR PACKAGES
                        </a>
                    </div>
                    <img src={LineFull} alt="Decorative line" />
                </div>
            </section>

            <section
                className="page-section respitesec notoppaddsec"
                id="toplftcarous"
            >
                <div className="container">
                    <div className="container flexwrap">
                        <div className="openplft">
                            <div
                                id="myCarousel3"
                                className="carousel slide"
                                data-ride="carousel"
                            >
                                <BirthdayPartiesSlider
                                    galleryImages={post.acf.bpGallery}
                                />
                            </div>
                        </div>
                        {/* <div className="whylistlft flexbox todwn">
                        <img
                            className="whiteborderimg"
                            src={
                                post.acf.bp_why_image.localFile.childImageSharp
                                    .fluid.src
                            }
                            alt="why hosting"
                        />
                    </div> */}

                        <div className="whylistrgt flexbox toup">
                            <h2
                                className="bluetxt"
                                dangerouslySetInnerHTML={{
                                    __html: post.acf.bpWhyTitle,
                                }}
                            />
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: post.acf.bpWhyReasons,
                                }}
                            />
                        </div>
                    </div>
                    {/* <div className="openplft">
                         <div
                            id="myCarousel3"
                            className="carousel slide display_none"
                            data-ride="carousel"
                        >
                            <BirthdayPartiesSlider
                                galleryImages={post.acf.bp_gallery}
                            />
                        </div>
                        
                        <div
                            className="display_none"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_book_online_code_widget,
                            }}
                        /> 
                    </div>*/}

                    {/* <div className="openprgt">
                        <h2
                            className="bluetxt display_none"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_title,
                            }}
                        />
                        <div
                        className="display_none"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_content,
                            }}
                        /> 
                    </div>*/}
                </div>
            </section>

            <section className="page-section centersec nopaddbottsec" id="packages-options">
                <div className="container smallestwdt">
                    <h2
                        className="bluetxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.bpPricingTitle,
                        }}
                    />
                    <p
                        dangerouslySetInnerHTML={{
                            __html: post.acf.bpPricingDescription,
                        }}
                    />

                    <img className="linemar nomartopimg" src={LineFull} alt="Decorative line" />

                    <div className="packagesdiv">
                        {post.acf.bpPricingList.map((item, i) => (
                            <div
                                className="pricingbox bg-primary text-white"
                                key={i}
                            >
                                <h2>
                                    <span
                                        className="yellowtxt"
                                        dangerouslySetInnerHTML={{
                                            __html: item.bpPriceTitle,
                                        }}
                                    />
                                </h2>
                                <h2>
                                    <span
                                        dangerouslySetInnerHTML={{
                                            __html: item.bpPriceAmmount,
                                        }}
                                    />
                                </h2>
                                <img src={PriceLine} alt="Price line" />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: item.bpPricePackageContent,
                                    }}
                                />
                            </div>
                        ))}
                    </div>
                    {/* <h3 class="bluetxt centerme">BIRTHDAY ADD-ONS</h3>
                    <div className='pricingtxt'><p>Call for more information!</p></div> */}
                    <div className="oplayhealwrap">
                        {/* <h5 class="bluetxt">Book online</h5> */}
                        {/* <div
                            className=""
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_book_online_code_widget,
                            }}
                        /> */}
                    </div>
                    <div className="oplayhealwrap">
                        <h5 className="bluetxt">Book Online</h5>
                        <iframe
                            src="https://wordpress-1396913-5383437.cloudwaysapps.com/healcode-bp.php"
                            title="Appointment booking"
                            className="healcode_widgets"
                        />
                    </div>
                </div>
            </section>

            <section
                className="whywelist page-section bg-primary text-white"
                id="openpbasic"
            >
                <div className="container middlewdt flexwrap">
                    <div className="flexbox toup">
                        <h2
                            className="yellowtxt"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bpWhatToExpectTitle,
                            }}
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bpWhatToExpectContent,
                            }}
                        />
                    </div>

                    <div className="flexbox todwn">
                        {post.acf.bpWhatToExpectImage?.localFile?.childImageSharp?.gatsbyImageData && (
                            <GatsbyImage
                                className="whiteborderimg"
                                image={getImage(post.acf.bpWhatToExpectImage.localFile.childImageSharp.gatsbyImageData)}
                                alt="What to expect"
                            />
                        )}
                    </div>
                </div>
                {/* <div className="container flexwrap">
                    <div className="whylistlft flexbox todwn">
                        <img
                            className="whiteborderimg"
                            src={
                                post.acf.bp_why_image.localFile.childImageSharp
                                    .fluid.src
                            }
                            alt="why hosting"
                        />
                    </div>

                    <div className="whylistrgt flexbox toup">
                        <h2
                            className="yellowtxt"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_why_title,
                            }}
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.bp_why_reasons,
                            }}
                        />
                    </div>
                </div> */}
            </section>

            <section className="page-section centersec notoppaddsec">
                <div className="container smallestwdt">
                    <img className="linemar" src={LineFull} alt="Decorative line" />

                    <div className="greenlft">
                        <img src={GreenLogo} alt="Green products logo" />
                    </div>

                    <div className="greenrgt">
                        <h2 className="bluetxt">We Use Only Green Products</h2>
                        <p className="biggertxt lastitem">
                            We believe in keeping our precious ones healthy and
                            safe. Our gym is cleaned daily at the end of the day
                            with only green products.
                        </p>
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default BirthDay;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            seo {
                title
                metaDesc
            }
            acfBirthdayParties {
                bpPricingTitle
                bpPricingDescription
                bpPricingList {
                    bpPriceTitle
                    bpPriceAmmount
                    bpPricePackageContent
                }
                bpWhyTitle
                bpWhyReasons
                bpWhatToExpectTitle
                bpWhatToExpectContent
                bpWhatToExpectImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 400
                                placeholder: BLURRED
                            )
                        }
                    }
                }
                bpGallery {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 800
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
        }
    }
`;