import { useStaticQuery, graphql } from 'gatsby';

export const useEventsHome = () => {
    const data = useStaticQuery(graphql`
        query GET_EVENTS_HOMEPAGE {
            allWpEventsPostType(limit: 3, sort: {date: DESC}) {
                nodes {
                    id
                    title
                    slug
                    acf {
                        event_date
                    }
                    featuredImage {
                        node {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                                }
                            }
                            altText
                        }
                    }
                }
            }
        }
    `);

    if (!data?.allWpEventsPostType?.nodes) {
        console.error('No events found for the homepage');
        return [];
    }

    return data.allWpEventsPostType.nodes.map(event => ({
        id: event.id,
        title: event.title,
        slug: event.slug,
        eventDate: event.acf?.event_date || 'Date not available',
        featuredImage: event.featuredImage?.node?.localFile?.childImageSharp
            ? {
                  image: event.featuredImage.node.localFile.childImageSharp.gatsbyImageData,
                  altText: event.featuredImage.node.altText || 'Event image',
              }
            : null,
    }));
};
