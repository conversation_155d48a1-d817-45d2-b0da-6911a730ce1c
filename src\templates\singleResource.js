import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import SEO from '../components/seo';
import '../styles/app.scss';

const SingleResources = ({ data }) => {
    const { wpResourcesPostType: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Resources" />
            <section className="page-section">
                <div className="container blogwrapper blogarticle">
                    <div className="bloglft">
                        <div className="blogimg">
                            {post.featuredImage?.node?.localFile?.childImageSharp?.gatsbyImageData && (
                                <img
                                    src={post.featuredImage.node.localFile.childImageSharp.gatsbyImageData.images.fallback.src}
                                    alt={post.title || 'Resource Image'}
                                />
                            )}
                        </div>
                        <div className="bloxexc">
                            <h2
                                dangerouslySetInnerHTML={{
                                    __html: post.title,
                                }}
                            />
                        </div>
                        <img
                            className="blogline"
                            src={LineFull}
                            alt="linefull"
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.content,
                            }}
                        />
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default SingleResources;

export const pageQuery = graphql`
    query SingleResource($id: String!) {
        wpResourcesPostType(id: { eq: $id }) {
            id
            title
            slug
            content
            seo {
                title
                metaDesc
            }
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 800
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
        }
    }
`;
