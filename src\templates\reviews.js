import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import WhatParentsSay from '../components/pages/WhatParentsSay';
import YelpReviews from '../components/pages/YelpReviews';
import SEO from '../components/seo';
import '../styles/app.scss';

const Reviews = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <WhatParentsSay />
            <YelpReviews yelpReviews={post.acf.yelp_reviews} />
            <Footer />
        </>
    );
};

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            ... on WpACFReviews {
                yelp_reviews {
                    yelp_review_code
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;

export default Reviews;
