import React from 'react';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';

const PosterDownload = data => (
    <section className="page-section" id="whyposter">
        <div className="container smallestwdt">
            <h2 className="bluetxt">Poster Download</h2>
            <h4
                dangerouslySetInnerHTML={{
                    __html: data.posterContent,
                }}
            />
            <h3 className="greentxt">DOWNLOAD THE WHY WE ROCK POSTER</h3>
            <GatsbyImage
                className="whyposterimg"
                image={getImage(data.posterImage.localFile)}
                alt="poster"
            />
            <a
                href={data.posterImage.localFile.publicURL}
                target="_blank"
                rel="noopener noreferrer"
                className="wrtsbtn yellowbtn"
            >
                DOWNLOAD
            </a>
        </div>
    </section>
);

export default PosterDownload;
