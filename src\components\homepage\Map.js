import React from 'react';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import { useLocationInfo } from '../../hooks/getLocationInfo';

const Map = () => {
    const locationData = useLocationInfo();
    return (
        <div className="mapwrap">
            <GatsbyImage
                className="map_bg"
                image={getImage(
                    locationData.map_background_image.localFile
                )}
                alt="map location"
                loading="lazy"
            />
            <h2 className="bluetxt">Our Location</h2>
            <GatsbyImage
                image={getImage(locationData.map_logo.localFile)}
                alt="map logo"
                loading="lazy"
            />
            <a
                href={locationData.get_directions_link}
                target="_blank"
                className="wrtsbtn bluebtn"
                rel="noopener noreferrer"
            >
                GET DIRECTIONS
            </a>
        </div>
    );
};

export default Map;
