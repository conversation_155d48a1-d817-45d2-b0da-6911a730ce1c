import React from 'react';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import Hero from '../components/homepage/Hero';
import Services from '../components/homepage/Services';
import DownloadApp from '../components/homepage/DownloadApp';
import Testimonials from '../components/homepage/Testimonials';
import Staff from '../components/homepage/Staff';
import Blog from '../components/homepage/Blog';
import Events from '../components/homepage/Events';
import Map from '../components/homepage/Map';
import '../styles/app.scss';
import { useTestimonials } from '../hooks/getHomeTestimonials';
import { useLatestBlog } from '../hooks/getLatestBlog';
import { useHomeSeo } from '../hooks/getHomeSEO';
import SEO from '../components/seo';

const IndexPage = () => {
    const dataTestimonials = useTestimonials();
    const testimonialsList = dataTestimonials?.testimonialsList || [];

    const dataSEO = useHomeSeo();
    const homeSEO = dataSEO || {};

    const dataBlog = useLatestBlog();
    const blogList = dataBlog || [];

    const seoTitle = homeSEO.yoastTitle || 'Homepage';
    const seoDescription = homeSEO.yoastMeta || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <Hero />
            <Services />
            <DownloadApp />
            <Testimonials testimonialsList={testimonialsList} />
            <Staff bgType="secondary" />
            <Blog blogList={blogList} />
            <Events />
            <Map />
            <Footer />
        </>
    );
};

export default IndexPage;
