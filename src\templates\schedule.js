import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import SEO from '../components/seo';
// images
import WhyImg1 from '../images/why1.jpg';
import WhyImg2 from '../images/why2.jpg';
import '../styles/app.scss';

const Schedule = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    const inlineCss = {
        maxWidth: '600px',
        marginTop: '50px',
    };

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt centersec">
                <div className="container">
                    <div
                        dangerouslySetInnerHTML={{
                            __html: post.content,
                        }}
                    />

                    {/* <a
                        className="wrtsbtn yellowbtn healcode-register"
                        style={inlineCss}
                        href="https://clients.mindbodyonline.com/ASP/su1.asp?studioid=5725512&tg=&vt=&lvl=&stype=&view=&trn=0&page=&catid=&prodid=&date=4%2f22%2f2022&classid=0&prodGroupId=&sSU=&optForwardingLink=&qParam=&justloggedin=&nLgIn=&pMode=0&loc=1"
                        target="_blank"
                    >
                        To sign up for our upcoming Classes/Special Events -
                        Click Here
                    </a> */}
                    <iframe
                        src="https://calendar.google.com/calendar/embed?src=werockthespectrummcallen%40gmail.com&ctz=UTC"
                        className="googlecal"
                    />
                </div>
            </section>
                        <Footer />
        </>
    );
};

export default Schedule;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            aCFSchedule {
                scheduleIframeSrc
                storyTimeTitle
                storyTimeContent
                storyTimeImage {
                    localFile {
                        childImageSharp {
                            fluid {
                                srcWebp
                                src
                            }
                        }
                    }
                }
                musicClassesTitle
                musicClassesContent
                musicClassesImage {
                    localFile {
                        childImageSharp {
                            fluid {
                                srcWebp
                                src
                            }
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;