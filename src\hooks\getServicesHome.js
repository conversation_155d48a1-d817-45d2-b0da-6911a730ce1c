import { useStaticQuery, graphql } from 'gatsby';

export const useServicesHome = () => {
    const data = useStaticQuery(graphql`
        query GET_SERVICES_HOME {
            wpPage(slug: { eq: "homepage" }) {
                title
                content
            }
        }
    `);

    if (!data?.wpPage) {
        console.error('Service boxes data not found');
        return [];
    }

    // Temporary fallback values until ACF is configured
    return [
        {
            serviceTitle: 'Open Play',
            serviceDescription: 'ACF fields are being configured. Service content will be restored once WordPress ACF integration is complete.',
            serviceLink: '#',
            serviceImage: null,
        },
        {
            serviceTitle: 'Birthday Parties',
            serviceDescription: 'ACF fields are being configured. Service content will be restored once WordPress ACF integration is complete.',
            serviceLink: '#',
            serviceImage: null,
        }
    ];


};
