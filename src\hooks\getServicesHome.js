import { useStaticQuery, graphql } from 'gatsby';

export const useServicesHome = () => {
    const data = useStaticQuery(graphql`
        query GET_SERVICES_HOME {
            wpPage(slug: { eq: "homepage" }) {
                aCFServicesHomepage {
                    serviceBoxesList {
                        serviceTitle
                        serviceDescription
                        serviceLink
                        serviceImage {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.aCFServicesHomepage?.serviceBoxesList) {
        console.error('Service boxes data not found');
        return [];
    }

    return data.wpPage.aCFServicesHomepage.serviceBoxesList.map(service => ({
        title: service.serviceTitle || '',
        description: service.serviceDescription || '',
        link: service.serviceLink || '',
        image: service.serviceImage?.localFile?.childImageSharp
            ? service.serviceImage.localFile.childImageSharp.gatsbyImageData
            : null,
    }));
};
