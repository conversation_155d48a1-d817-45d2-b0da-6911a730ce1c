import { useStaticQuery, graphql } from 'gatsby';

export const useServicesHome = () => {
    const data = useStaticQuery(graphql`
        query GET_SERVICES_HOME {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    service_boxes_list {
                        service_title
                        service_description
                        service_link
                        service_image {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf?.service_boxes_list) {
        console.error('Service boxes data not found');
        return [];
    }

    return data.wpPage.acf.service_boxes_list.map(service => ({
        title: service.service_title || '',
        description: service.service_description || '',
        link: service.service_link || '',
        image: service.service_image?.localFile?.childImageSharp
            ? service.service_image.localFile.childImageSharp.gatsbyImageData
            : null,
    }));
};
