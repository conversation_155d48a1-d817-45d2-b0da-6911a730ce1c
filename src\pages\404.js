import React from 'react';
import { Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import oopline from '../images/oopsline.png';
import girl404 from '../images/404girl.jpg';
import { useLatestBlog } from '../hooks/getLatestBlog';
import '../styles/app.scss';

const NotFoundPage = () => {
    const dataBlog = useLatestBlog();

    return (
        <>
            <HeaderMain />
            <section className="page-section smallestwdt nopaddbottsec">
                <div className="container">
                    <div className="oopstxt">
                        <h2 className="bluetxt">404 oops!</h2>
                        <p className="greentxt">Something went wrong.</p>
                    </div>
                    <div className="oopsimg">
                        <img src={girl404} alt="A girl looking confused" />
                    </div>
                </div>
                <img className="oopsline" src={oopline} alt="Decorative line" />
            </section>

            <section className="page-section smallwdt" id="oopseventswrap">
                <p>
                    The page you’re trying to access doesn’t seem to exist. Here
                    are some helpful resources instead:
                </p>
                <div className="container container_404">
                    {dataBlog && dataBlog.length > 0 ? (
                        dataBlog.map(article => (
                            <Link
                                to={`/${article.slug}`}
                                className="homeeventbox"
                                key={article.id}
                            >
                                {article.featuredImage && (
                                    <GatsbyImage
                                        image={getImage(article.featuredImage)}
                                        alt={article.title || 'Blog post image'}
                                    />
                                )}
                                <h5
                                    className="bluetxt"
                                    dangerouslySetInnerHTML={{
                                        __html: article.title,
                                    }}
                                />
                                <p>
                                    {new Intl.DateTimeFormat('en-US', {
                                        month: 'long',
                                        day: 'numeric',
                                        year: 'numeric',
                                    }).format(new Date(article.date))}
                                </p>
                            </Link>
                        ))
                    ) : (
                        <p>No blog posts available at the moment.</p>
                    )}
                </div>
            </section>
            <Footer />
        </>
    );
};

export default NotFoundPage;
