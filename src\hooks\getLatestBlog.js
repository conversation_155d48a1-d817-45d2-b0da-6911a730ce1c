import { useStaticQuery, graphql } from 'gatsby';

export const useLatestBlog = () => {
    const data = useStaticQuery(
        graphql`
            query GET_LATEST_POSTS {
                allWpPost(limit: 5, sort: {date: DESC}) {
                    nodes {
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            layout: CONSTRAINED
                                            width: 800
                                        )
                                    }
                                }
                            }
                        }
                        categories {
                            nodes {
                                name
                            }
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                    }
                }
            }
        `
    );

    if (!data?.allWpPost?.nodes) {
        console.error('No latest blog posts found');
        return [];
    }

    return data.allWpPost.nodes.map(post => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        date: post.date,
        excerpt: post.excerpt,
        categories: post.categories?.nodes?.map(category => category.name) || [],
        featuredImage: post.featuredImage?.node?.localFile?.childImageSharp
            ? post.featuredImage.node.localFile.childImageSharp.gatsbyImageData
            : null,
    }));
};
