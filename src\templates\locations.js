import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LocationsList from '../components/pages/LocationsList';
import SEO from '../components/seo';
import '../styles/app.scss';

const Locations = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section
                className="page-section bg-secondary text-white"
                id="locgreen"
            >
                <div className="container smallestwdt">
                    {/* <h2 className="yellowtxt">
                        <span>122 Destinations</span>
                        <span className="commaloc">,</span>{' '}
                        <span>27 States</span>
                        <span className="commaloc">,</span>{' '}
                        <span>8 Countries</span>
                    </h2> */}
                    <h2
                        className="yellowtxt locnumbers"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.locationsTitle,
                        }}
                    />
                    <h4>
                        We strive to create the best sensory gym for autism and
                        provide the best indoor playground franchise opportunity
                        available. Here at WRTS, we want your gym to be THE KIDS
                        GYM in your city.
                        <br />
                        Let’s grow your play franchise together.
                    </h4>
                </div>
            </section>
            <LocationsList
                locationsUsa={post.acf.listLocationsUsa}
                locationsUsaCs={post.acf.listLocationsComingSoonToUsa}
                locationsInt={post.acf.listLocationsInternational}
                locationsIntCs={post.acf.listLocationsComingSoonInternational}
            />
            {/* <LocationsPage /> */}
            <Footer />
        </>
    );
};

export default Locations;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            aCFLocations {
                locationsTitle
                listLocationsUsa {
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    contactUsUrl
                    locationImageUsa {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 400
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
                listLocationsInternational {
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    contactUsUrl
                    locationImageInt {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 400
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
                listLocationsComingSoonToUsa {
                    contactUsUrl
                    locationInfo
                    locationName
                    viewOnMapUrl
                    viewPhotosUrl
                    visitWebsiteUrl
                    locationImageCUsa {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 400
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
                listLocationsComingSoonInternational {
                    contactUsUrl
                    visitWebsiteUrl
                    viewPhotosUrl
                    viewOnMapUrl
                    locationName
                    locationInfo
                    locationImageCInt {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 400
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;