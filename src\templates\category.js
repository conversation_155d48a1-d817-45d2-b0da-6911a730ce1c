import React from 'react';
import { Link, graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import '../styles/app.scss';
import SEO from '../components/seo';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';

const CategoryPage = props => {
    const { data, pageContext } = props;
    const { name: category } = pageContext;

    return (
        <>
            <SEO title={`${category} archive`} />
            <HeaderMain />
            <HeroSingle pageTitle={category} />
            <section className="page-section">
                <div className="container blogwrapper">
                    <div className="bloglftwrap">
                        {data.allWpPost.nodes.map(post => (
                            <div className="bloglft" key={post.id}>
                                <div className="blogimg">
                                    {post.featuredImage?.node?.localFile && (
                                        <GatsbyImage
                                            image={getImage(
                                                post.featuredImage.node.localFile
                                            )}
                                            alt={post.title || 'Blog post image'}
                                        />
                                    )}
                                </div>
                                <div className="bloxexc">
                                    <Link
                                        to={`/${post.slug}`}
                                        className="postName"
                                    >
                                        <h2
                                            dangerouslySetInnerHTML={{
                                                __html: post.title,
                                            }}
                                        />
                                    </Link>
                                    <h5>
                                        {new Intl.DateTimeFormat('en-US', {
                                            month: 'long',
                                            day: 'numeric',
                                            year: 'numeric',
                                        }).format(new Date(post.date))}
                                    </h5>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: post.excerpt,
                                        }}
                                    />
                                    <Link to={`/${post.slug}`}>Read More</Link>
                                </div>
                                <img
                                    className="blogline"
                                    src={LineFull}
                                    alt="linefull"
                                />
                            </div>
                        ))}
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default CategoryPage;

export const pageQuery = graphql`
    query CategoryPage($slug: String!) {
        allWpPost(
            filter: { categories: { nodes: { elemMatch: { slug: { eq: $slug } } } } }
        ) {
            nodes {
                featuredImage {
                    node {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
                categories {
                    nodes {
                        name
                        slug
                    }
                }
                date
                excerpt
                slug
                title
                id
            }
        }
    }
`;

