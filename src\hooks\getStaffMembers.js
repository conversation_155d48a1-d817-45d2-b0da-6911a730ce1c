import { useStaticQuery, graphql } from 'gatsby';

export const useStaffMembers = () => {
    const data = useStaticQuery(graphql`
        query GET_STAFF_MEMBERS {
            allWpAcfOptions {
                nodes {
                    options {
                        our_staff_section_title
                        our_staff_list_members {
                            staff_member_fullname
                            staff_member_position
                            staff_member_image {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(layout: CONSTRAINED, width: 400)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.allWpAcfOptions?.nodes?.[0]?.options) {
        console.error('Staff member data not found');
        return {
            sectionTitle: '',
            staffMembers: [],
        };
    }

    const { our_staff_section_title, our_staff_list_members } =
        data.allWpAcfOptions.nodes[0].options;

    return {
        sectionTitle: our_staff_section_title || '',
        staffMembers: (our_staff_list_members || []).map(member => ({
            fullName: member.staff_member_fullname || '',
            position: member.staff_member_position || '',
            image: member.staff_member_image?.localFile?.childImageSharp
                ? member.staff_member_image.localFile.childImageSharp.gatsbyImageData
                : null,
        })),
    };
};
