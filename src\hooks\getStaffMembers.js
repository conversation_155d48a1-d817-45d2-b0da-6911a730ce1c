import { useStaticQuery, graphql } from 'gatsby';

export const useStaffMembers = () => {
    const data = useStaticQuery(graphql`
        query GET_STAFF_MEMBERS {
            wp {
                generalSettings {
                    title
                }
            }
        }
    `);

    if (!data?.wp) {
        console.error('Staff member data not found');
        return {
            sectionTitle: '',
            staffMembers: [],
        };
    }

    // Temporary fallback values until ACF is configured
    return {
        sectionTitle: 'Our Staff',
        staffMembers: [
            {
                staffMemberFullname: 'Staff Member',
                staffMemberPosition: 'ACF fields are being configured',
                staffMemberImage: null,
            }
        ],
    };

    const { ourStaffSectionTitle, ourStaffListMembers } = data.wp;

    return {
        sectionTitle: ourStaffSectionTitle || '',
        staffMembers: (ourStaffListMembers || []).map(member => ({
            fullName: member.staffMemberFullname || '',
            position: member.staffMemberPosition || '',
            image: member.staffMemberImage?.localFile?.childImageSharp
                ? member.staffMemberImage.localFile.childImageSharp.gatsbyImageData
                : null,
        })),
    };
};
