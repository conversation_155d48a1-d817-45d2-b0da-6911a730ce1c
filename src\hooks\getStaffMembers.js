import { useStaticQuery, graphql } from 'gatsby';

export const useStaffMembers = () => {
    const data = useStaticQuery(graphql`
        query GET_STAFF_MEMBERS {
            wp {
                acf {
                    ourStaffSectionTitle
                    ourStaffListMembers {
                        staffMemberFullname
                        staffMemberPosition
                        staffMemberImage {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wp) {
        console.error('Staff member data not found');
        return {
            sectionTitle: '',
            staffMembers: [],
        };
    }

    const { ourStaffSectionTitle, ourStaffListMembers } = data.wp;

    return {
        sectionTitle: ourStaffSectionTitle || '',
        staffMembers: (ourStaffListMembers || []).map(member => ({
            fullName: member.staffMemberFullname || '',
            position: member.staffMemberPosition || '',
            image: member.staffMemberImage?.localFile?.childImageSharp
                ? member.staffMemberImage.localFile.childImageSharp.gatsbyImageData
                : null,
        })),
    };
};
