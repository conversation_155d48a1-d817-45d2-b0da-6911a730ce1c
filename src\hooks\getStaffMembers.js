import { useStaticQuery, graphql } from 'gatsby';

export const useStaffMembers = () => {
    const data = useStaticQuery(graphql`
        query GET_STAFF_MEMBERS {
            allWpAcfOptions {
                nodes {
                    options {
                        ourStaffSectionTitle
                        ourStaffListMembers {
                            staffMemberFullname
                            staffMemberPosition
                            staffMemberImage {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(layout: CONSTRAINED, width: 400)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.allWpAcfOptions?.nodes?.[0]?.options) {
        console.error('Staff member data not found');
        return {
            sectionTitle: '',
            staffMembers: [],
        };
    }

    const { ourStaffSectionTitle, ourStaffListMembers } =
        data.allWpAcfOptions.nodes[0].options;

    return {
        sectionTitle: ourStaffSectionTitle || '',
        staffMembers: (ourStaffListMembers || []).map(member => ({
            fullName: member.staffMemberFullname || '',
            position: member.staffMemberPosition || '',
            image: member.staffMemberImage?.localFile?.childImageSharp
                ? member.staffMemberImage.localFile.childImageSharp.gatsbyImageData
                : null,
        })),
    };
};
