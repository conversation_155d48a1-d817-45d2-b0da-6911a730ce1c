import { useStaticQuery, graphql } from 'gatsby';

export const useHeroContent = () => {
    const data = useStaticQuery(graphql`
        query GET_HERO_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                title
                content
            }
        }
    `);

    if (!data?.wpPage) {
        console.error('Hero content not found');
        return {
            heroWelcomeTitle: '',
            heroSubtitle: '',
            heroButtonLink: '',
            heroButtonText: '',
            heroVideoMp4: '',
            heroVideoWebp: '',
        };
    }

    // Temporary fallback values until ACF is configured
    const heroWelcomeTitle = data.wpPage.title || 'Welcome';
    const heroSubtitle = 'ACF fields are being configured';
    const heroButtonLink = '#';
    const heroButtonText = 'Learn More';
    const heroVideoFileMp4 = null;
    const heroVideoFileWebp = null;

    return {
        heroWelcomeTitle,
        heroSubtitle,
        heroButtonLink,
        heroButtonText,
        heroVideoMp4: '',
        heroVideoWebp: '',
    };
};
