import { useStaticQuery, graphql } from 'gatsby';

export const useHeroContent = () => {
    const data = useStaticQuery(graphql`
        query GET_HERO_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    heroWelcomeTitle
                    heroSubtitle
                    heroButtonLink
                    heroButtonText
                    heroVideoFileMp4 {
                        localFile {
                            publicURL
                        }
                    }
                    heroVideoFileWebp {
                        localFile {
                            publicURL
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Hero content not found');
        return {
            heroWelcomeTitle: '',
            heroSubtitle: '',
            heroButtonLink: '',
            heroButtonText: '',
            heroVideoMp4: '',
            heroVideoWebp: '',
        };
    }

    const {
        heroWelcomeTitle,
        heroSubtitle,
        heroButtonLink,
        heroButtonText,
        heroVideoFileMp4,
        heroVideoFileWebp,
    } = data.wpPage.acf;

    return {
        heroWelcomeTitle,
        heroSubtitle,
        heroButtonLink,
        heroButtonText,
        heroVideoMp4: heroVideoFileMp4?.localFile?.publicURL || '',
        heroVideoWebp: heroVideoFileWebp?.localFile?.publicURL || '',
    };
};
