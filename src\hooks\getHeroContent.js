import { useStaticQuery, graphql } from 'gatsby';

export const useHeroContent = () => {
    const data = useStaticQuery(graphql`
        query GET_HERO_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    heroWelcomeTitle
                    heroSubtitle
                    heroButtonLink
                    heroButtonText
                    hero_video_file_mp4 {
                        localFile {
                            publicURL
                        }
                    }
                    hero_video_file_webp {
                        localFile {
                            publicURL
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Hero content not found');
        return {
            heroWelcomeTitle: '',
            heroSubtitle: '',
            heroButtonLink: '',
            heroButtonText: '',
            heroVideoMp4: '',
            heroVideoWebp: '',
        };
    }

    const {
        heroWelcomeTitle,
        heroSubtitle,
        heroButtonLink,
        heroButtonText,
        hero_video_file_mp4,
        hero_video_file_webp,
    } = data.wpPage.acf;

    return {
        heroWelcomeTitle,
        heroSubtitle,
        heroButtonLink,
        heroButtonText,
        heroVideoMp4: hero_video_file_mp4?.localFile?.publicURL || '',
        heroVideoWebp: hero_video_file_webp?.localFile?.publicURL || '',
    };
};
