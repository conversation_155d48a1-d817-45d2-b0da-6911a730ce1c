module.exports = {
    siteMetadata: {
        title: `Wrts Mig Test Sandro`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        `gatsby-plugin-sharp`,
        {
            resolve: `gatsby-plugin-image`,
            options: {
                // No additional options needed for now
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Mig Test Sandro`,
                short_name: `Wrts Mig Test Sandro`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // Path to favicon
            },
        },
        `gatsby-plugin-sass`,
        {
            resolve: `gatsby-source-wordpress`,
            options: {
                url: `https://wordpress-1396913-5383437.cloudwaysapps.com/graphql`,
                schema: {
                    timeout: 30000, // Increase timeout for large schemas
                },
                type: {
                    MediaItem: {
                        createFileNodes: true,
                    },
                    EventsPostType: {
                        limit: 100, // Adjust as needed
                    },
                    ResourcesPostType: {
                        limit: 100, // Adjust as needed
                    },
                },
            },
        },
        {
            resolve: `gatsby-plugin-purgecss`,
            options: {
                develop: true, // Enable purgecss in development
                purgeOnly: [`style/app.scss`], // Specify the SCSS file to purge
            },
        },
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                workboxConfig: {
                    skipWaiting: true, // Automatically activate new service workers
                    clientsClaim: true, // Take control of all clients immediately
                },
            },
        },
    ],
};
