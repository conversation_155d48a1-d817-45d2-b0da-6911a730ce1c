module.exports = {
    siteMetadata: {
        title: `Wrts Mig Test Sandro`,
        description: ``,
        author: `@gatsbyjs`,
    },
    plugins: [
        `gatsby-plugin-react-helmet`,
        {
            resolve: `gatsby-source-filesystem`,
            options: {
                name: `images`,
                path: `${__dirname}/src/images`,
            },
        },
        `gatsby-transformer-sharp`,
        {
            resolve: `gatsby-plugin-sharp`,
            options: {
                defaults: {
                    formats: [`auto`, `webp`],
                    placeholder: `blurred`,
                    quality: 90,
                    breakpoints: [750, 1080, 1366, 1920],
                },
            },
        },
        {
            resolve: `gatsby-plugin-image`,
            options: {
                // Modern image processing with optimizations
            },
        },
        {
            resolve: `gatsby-plugin-manifest`,
            options: {
                name: `Wrts Mig Test Sandro`,
                short_name: `Wrts Mig Test Sandro`,
                start_url: `/`,
                background_color: `#146fb8`,
                theme_color: `#146fb8`,
                display: `minimal-ui`,
                icon: `src/images/fav.png`, // Path to favicon
            },
        },
        `gatsby-plugin-sass`,
        {
            resolve: `gatsby-source-wordpress`,
            options: {
                url: `https://wordpress-1396913-5383437.cloudwaysapps.com/graphql`,
                schema: {
                    timeout: 60000, // Increase timeout for large schemas
                    requestConcurrency: 2, // Reduce concurrent requests
                    perPage: 20, // Reduce items per page
                },
                type: {
                    MediaItem: {
                        createFileNodes: true,
                    },
                    EventsPostType: {
                        limit: 20, // Reduce limit for better performance
                    },
                    ResourcesPostType: {
                        limit: 20, // Reduce limit for better performance
                    },
                },
                develop: {
                    hardCacheMediaFiles: true,
                },
                production: {
                    hardCacheMediaFiles: true,
                    allow404Images: true, // Allow missing images in production
                },
            },
        },
        {
            resolve: `gatsby-plugin-purgecss`,
            options: {
                develop: true, // Enable purgecss in development
                purgeOnly: [`style/app.scss`], // Specify the SCSS file to purge
            },
        },
        {
            resolve: `gatsby-plugin-offline`,
            options: {
                workboxConfig: {
                    skipWaiting: true, // Automatically activate new service workers
                    clientsClaim: true, // Take control of all clients immediately
                },
            },
        },
        `gatsby-plugin-netlify`,
    ],
};
