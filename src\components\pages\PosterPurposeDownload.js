import React from 'react';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import LineFull from '../../images/linefull.jpg';

const PosterPurposeDownload = purposeData => {
    const data = purposeData.posterData;
    return (
        <section className="page-section smallestwdt centersec nopaddtopsec">
            <div className="container purposeposter">
                <img className="purposeline" src={LineFull} alt="line full" />
                <h2
                    className="bluetxt"
                    dangerouslySetInnerHTML={{
                        __html: data.purpose_poster_title,
                    }}
                />
                <p
                    className="biggertxt"
                    dangerouslySetInnerHTML={{
                        __html: data.purpose_poster_description,
                    }}
                />

                <div className="threesessions">
                    <h5>Download PLAY WITH A PURPOSE&trade; Poster</h5>
                </div>
                <GatsbyImage
                    image={getImage(data.purpose_poster_image.localFile)}
                    alt="playposter"
                />
                <a
                    href={data.purpose_poster_image.localFile.publicURL}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="wrtsbtn yellowbtn"
                >
                    DOWNLOAD
                </a>
            </div>
        </section>
    );
};

export default PosterPurposeDownload;
