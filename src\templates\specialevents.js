import React from 'react';
import { graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import '../styles/app.scss';
import SEO from '../components/seo';

const SpecialEvents = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <section className="page-section smallestwdt" id="bookparty">
                <div className="container">
                    <h2
                        className="bluetxt"
                        dangerouslySetInnerHTML={{
                            __html: post.acf.se_title,
                        }}
                    />
                    <div className="bookawrap">
                        <a
                            href={`tel:${post.acf.se_call_us_number}`}
                            className="wrtsbtn yellowbtn fullbtn"
                        >
                            CALL US TODAY: {post.acf.se_call_us_number}
                        </a>
                    </div>
                    <img src={LineFull} alt="linefull" />
                </div>
            </section>
            <section className="page-section notoppaddsec">
                <div className="container flexwrap bdaytxtadjust">
                    <div className="whylistlft flexbox todwn">
                        {post.acf.se_book_image?.localFile?.childImageSharp?.gatsbyImageData && (
                            <GatsbyImage
                                image={getImage(post.acf.se_book_image.localFile.childImageSharp.gatsbyImageData)}
                                alt="Special events"
                            />
                        )}
                    </div>

                    <div className="whylistrgt flexbox toup">
                        <h2
                            className="bluetxt"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.se_book_title,
                            }}
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.acf.se_book_content,
                            }}
                        />
                    </div>
                </div>
            </section>
            {post.acf.special_events_list.map((box, i) => {
                if (i % 2 === 0 || i === 0) {
                    return (
                        <section
                            className="whywelist page-section bg-primary text-white specialtripsbtn"
                            key={i}
                        >
                            <div className="container flexwrap bdaytxtadjust">
                                <div className="whylistlft flexbox">
                                    <h2
                                        className="yellowtxt"
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_title,
                                        }}
                                    />
                                    <h4
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_content,
                                        }}
                                    />
                                    <a
                                        href={box.se_button_url}
                                        className="wrtsbtn yellowbtn inlinebtn"
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_button_text,
                                        }}
                                    />
                                </div>

                                <div className="whylistrgt flexbox">
                                    <div className="equipstarimgrgt">
                                        {box.se_box_image?.localFile?.childImageSharp?.gatsbyImageData && (
                                            <GatsbyImage
                                                className="whiteborderimg"
                                                image={getImage(box.se_box_image.localFile.childImageSharp.gatsbyImageData)}
                                                alt="Special event"
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                        </section>
                    );
                }
                if (i === 3) {
                    return (
                        <section
                            className="whywelist page-section bg-secondary text-white specialtripsbtn"
                            key={i}
                        >
                            <div className="container flexwrap bdaytxtadjust">
                                <div className="whylistrgt flexbox todwn">
                                    {box.se_box_image?.localFile?.childImageSharp?.gatsbyImageData && (
                                        <GatsbyImage
                                            className="whiteborderimg"
                                            image={getImage(box.se_box_image.localFile.childImageSharp.gatsbyImageData)}
                                            alt="Special event"
                                        />
                                    )}
                                </div>

                                <div className="whywelist flexbox toup">
                                    <h2
                                        className="yellowtxt"
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_title,
                                        }}
                                    />
                                    <h4
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_content,
                                        }}
                                    />
                                    <a
                                        href={box.se_button_url}
                                        className="wrtsbtn yellowbtn inlinebtn"
                                        dangerouslySetInnerHTML={{
                                            __html: box.se_box_button_text,
                                        }}
                                    />
                                </div>
                            </div>
                        </section>
                    );
                }
                return (
                    <section className="page-section specialtripsbtn" key={i}>
                        <div className="container flexwrap bdaytxtadjust">
                            <div className="whylistrgt flexbox todwn">
                                {box.se_box_image?.localFile?.childImageSharp?.gatsbyImageData && (
                                    <GatsbyImage
                                        className="whiteborderimg"
                                        image={getImage(box.se_box_image.localFile.childImageSharp.gatsbyImageData)}
                                        alt="Special event"
                                    />
                                )}
                            </div>

                            <div className="whywelist flexbox toup">
                                <h2
                                    className="bluetxt"
                                    dangerouslySetInnerHTML={{
                                        __html: box.se_box_title,
                                    }}
                                />
                                <h4
                                    dangerouslySetInnerHTML={{
                                        __html: box.se_box_content,
                                    }}
                                />
                                <a
                                    href={box.se_button_url}
                                    className="wrtsbtn yellowbtn inlinebtn"
                                    dangerouslySetInnerHTML={{
                                        __html: box.se_box_button_text,
                                    }}
                                />
                            </div>
                        </div>
                    </section>
                );
            })}
            <Footer />
        </>
    );
};

export default SpecialEvents;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            acf {
                se_book_content
                se_book_title
                se_call_us_number
                se_title
                se_book_image {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 600
                                placeholder: BLURRED
                            )
                        }
                    }
                }
                special_events_list {
                    se_box_button_text
                    se_box_content
                    se_box_title
                    se_button_url
                    se_box_image {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: CONSTRAINED
                                    width: 400
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;
