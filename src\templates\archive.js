import React from 'react';
import { graphql, Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import HeroSingle from '../components/pages/HeroSingle';
import Footer from '../components/Footer';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import '../styles/app.scss';

const Archive = ({ data, pageContext }) => {
    const { postId } = pageContext;
    const articles = data.allWpPost.nodes;

    // Filter posts based on postId
    const filteredPosts = articles.filter(article =>
        postId.includes(article.id)
    );

    return (
        <>
            <SEO title={pageContext.title} />
            <HeaderMain />
            <HeroSingle pageTitle={pageContext.title} />

            <section className="page-section">
                <div className="container blogwrapper">
                    <div className="bloglftwrap">
                        {filteredPosts.map(article => (
                            <div className="bloglft" key={article.id}>
                                <div className="blogimg">
                                    {article.featuredImage?.node?.localFile && (
                                        <GatsbyImage
                                            image={getImage(
                                                article.featuredImage.node.localFile
                                            )}
                                            alt={article.title || 'Blog post image'}
                                        />
                                    )}
                                </div>
                                <div className="bloxexc">
                                    <Link
                                        to={`/${article.slug}`}
                                        className="postName"
                                    >
                                        <h2
                                            dangerouslySetInnerHTML={{
                                                __html: article.title,
                                            }}
                                        />
                                    </Link>
                                    <h5>
                                        {new Intl.DateTimeFormat('en-US', {
                                            month: 'long',
                                            day: 'numeric',
                                            year: 'numeric',
                                        }).format(new Date(article.date))}
                                    </h5>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: article.excerpt,
                                        }}
                                    />
                                    <Link to={`/${article.slug}`}>
                                        Read More
                                    </Link>
                                </div>
                                <img
                                    className="blogline"
                                    src={LineFull}
                                    alt="Decorative line"
                                />
                            </div>
                        ))}
                    </div>

                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default Archive;

export const pageQuery = graphql`
    query {
        allWpPost {
            nodes {
                id
                slug
                categories {
                    name
                    slug
                }
                excerpt
                title
                date
                featuredImage {
                    node {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
            }
        }
    }
`;
