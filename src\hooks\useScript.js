import { useEffect } from 'react';

const useScript = url => {
    useEffect(() => {
        // Check if the script is already added
        const existingScript = document.querySelector(`script[src="${url}"]`);
        if (existingScript) {
            return;
        }

        const script = document.createElement('script');
        script.src = url;
        script.async = true;

        // Add error handling
        script.onerror = () => {
            console.error(`Failed to load script: ${url}`);
        };

        document.body.appendChild(script);

        return () => {
            // Cleanup: Remove the script if it exists
            if (script.parentNode) {
                script.parentNode.removeChild(script);
            }
        };
    }, [url]);
};

export default useScript;
