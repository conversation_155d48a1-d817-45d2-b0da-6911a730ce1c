import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import SEO from '../components/seo';
import HeroSingle from '../components/pages/HeroSingle';
import WhyWe from '../components/pages/WhyWe';
import SpecializedEquipmentList from '../components/pages/SpecializedEquipmentList';
import PosterDownload from '../components/pages/PosterDownload';
import LineFull from '../images/linefull.jpg';
import '../styles/app.scss';

const WhyWeRock = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />
            <WhyWe content={post.content} featImage={post.featuredImage?.node} />
            <SpecializedEquipmentList
                eqList={post.acf.specializedPiecesList}
                eqTitle={post.acf.equipmentTitle}
            />
            <section>
                <div className="container">
                    <img
                        className="margin-0-auto"
                        src={LineFull}
                        alt="line full"
                    />
                </div>
            </section>
            <PosterDownload
                posterImage={post.acf.wwrPosterImage}
                posterContent={post.acf.wwrPosterDownloadContent}
            />
            <Footer />
        </>
    );
};

export default WhyWeRock;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: FULL_WIDTH
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
            content
            acf {
                equipmentTitle
                specializedPiecesList {
                    equipmentName
                    equipmentImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: FULL_WIDTH
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                    equipmentDescription
                    equipmentEncourages
                    equipmentStarImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(
                                    layout: FULL_WIDTH
                                    placeholder: BLURRED
                                )
                            }
                        }
                    }
                }
                wwrPosterDownloadContent
                wwrPosterImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: FULL_WIDTH
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
        }
    }
`;

