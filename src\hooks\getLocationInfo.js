import { useStaticQuery, graphql } from 'gatsby';

export const useLocationInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_LOCATION_INFO {
            wp {
                locationInfo {
                    acfLocationInfo {
                        mapBackgroundImage {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: FULL_WIDTH)
                                }
                            }
                        }
                        mapLogo {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 200)
                                }
                            }
                        }
                        getDirectionsLink
                    }
                }
            }
        }
    `);

    if (!data?.wp?.locationInfo?.acfLocationInfo) {
        console.error('Location information not found');
        return {
            mapBackgroundImage: null,
            mapLogo: null,
            getDirectionsLink: '',
        };
    }

    const { mapBackgroundImage, mapLogo, getDirectionsLink } = data.wp.locationInfo.acfLocationInfo;

    return {
        mapBackgroundImage: mapBackgroundImage?.localFile?.childImageSharp
            ? mapBackgroundImage.localFile.childImageSharp.gatsbyImageData
            : null,
        mapLogo: mapLogo?.localFile?.childImageSharp
            ? mapLogo.localFile.childImageSharp.gatsbyImageData
            : null,
        getDirectionsLink: getDirectionsLink || '',
    };
};
