import { useStaticQuery, graphql } from 'gatsby';

export const useLocationInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_LOCATION_INFO {
            wp {
                generalSettings {
                    title
                }
            }
        }
    `);

    if (!data?.wp) {
        console.error('Location information not found');
        return {
            mapBackgroundImage: null,
            mapLogo: null,
            getDirectionsLink: '',
        };
    }

    // Temporary fallback values until ACF is configured
    return {
        mapBackgroundImage: null,
        mapLogo: null,
        getDirectionsLink: '#',
    };
};
