import { useStaticQuery, graphql } from 'gatsby';

export const useLocationInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_LOCATION_INFO {
            wpPage(slug: { eq: "locations" }) {
                acf {
                    map_background_image {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: FULL_WIDTH)
                            }
                        }
                    }
                    map_logo {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 200)
                            }
                        }
                    }
                    get_directions_link
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Location information not found');
        return {
            mapBackgroundImage: null,
            mapLogo: null,
            getDirectionsLink: '',
        };
    }

    const { map_background_image, map_logo, get_directions_link } = data.wpPage.acf;

    return {
        mapBackgroundImage: map_background_image?.localFile?.childImageSharp
            ? map_background_image.localFile.childImageSharp.gatsbyImageData
            : null,
        mapLogo: map_logo?.localFile?.childImageSharp
            ? map_logo.localFile.childImageSharp.gatsbyImageData
            : null,
        getDirectionsLink: get_directions_link || '',
    };
};
