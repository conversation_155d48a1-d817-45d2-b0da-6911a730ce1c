import React from 'react';
import { graphql, Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';

import '../styles/app.scss';

import { useResourcesTeam } from '../hooks/getResourceTeam';

const Resources = ({ data }) => {
    const post = data.wpPage; // Updated to use the new schema
    const resourcesList = useResourcesTeam();

    // Modernized SEO logic with null checks
    const new_seo_title = post.seo?.title?.replace('&#039;', "'") || post.title;

    return (
        <>
            <SEO
                title={new_seo_title}
                meta={post.seo?.metaDesc}
                description={post.seo?.metaDesc}
            />
            <HeaderMain />
            <HeroSingle pageTitle="Resources" />

            <section className="page-section">
                <div className="container blogwrapper reswrap smallestwdt">
                    <h2 className="bluetxt soonplaceholder">COMING SOON</h2>
                    <div className="bloglftwrap">
                        {resourcesList.map(resource => (
                            <div className="bloglft" key={resource.id}>
                                <div className="blogimg">
                                    <GatsbyImage
                                        image={getImage(
                                            resource.featuredImage?.node
                                                ?.localFile
                                        )}
                                        alt={
                                            resource.featuredImage?.node
                                                ?.altText || 'Resource Image'
                                        }
                                    />
                                </div>
                                <div className="bloxexc">
                                    <Link
                                        to={`/resources/${resource.slug}`}
                                        className="postName"
                                    >
                                        <h2
                                            dangerouslySetInnerHTML={{
                                                __html: resource.title,
                                            }}
                                        />
                                    </Link>
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: resource.excerpt,
                                        }}
                                    />
                                    <Link to={`/resources/${resource.slug}`}>
                                        Read More
                                    </Link>
                                </div>
                                <img
                                    className="blogline"
                                    src={LineFull}
                                    alt="linefull"
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default Resources;

export const pageQuery = graphql`
    query ($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            seo {
                title
                metaDesc
            }
        }
    }
`;
