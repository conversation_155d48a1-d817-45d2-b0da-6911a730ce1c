/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on December 13, 2019 */
@font-face {
  font-family: "gothamblack";
  src: url("../fonts/gotham-black-webfont.woff2") format("woff2"),
    url("../fonts/gotham-black-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "gothambook";
  src: url("../fonts/gotham_book_regular-webfont.woff2") format("woff2"),
    url("../fonts/gotham_book_regular-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "hogfish_demoregular";
  src: url("../fonts/hogfish-webfont.woff2") format("woff2"),
    url("../fonts/hogfish-webfont.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "si";
  src: url("../fonts/socicon-v1.5/socicon.eot");
  src: url("../fonts/socicon-v1.5/socicon.woff") format("woff"),
    url("../fonts/socicon-v1.5/socicon.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  @font-face {
    font-family: si;
    src: url(../fonts/socicon-v1.5/socicon.svg) format(svg);
    font-display: swap;
  }
}

.soc {
  overflow: hidden;
  margin: 0;
  padding: 0;
  list-style: none;
}
.soc a {
  font-family: si !important;
  font-style: normal;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-decoration: none;
  text-align: center;
  display: block;
  position: relative;
  z-index: 1;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
  margin-right: 7px;
  color: #000000;
  background-color: transparent;
}
.soc-icon-last {
  margin: 0 !important;
}
.soc-facebook:before {
  content: "\e041";
}
.soc-twitter:before {
  content: "\e040";
}
.soc-instagram:before {
  content: "\e057";
}
.soc-youtube:before {
  content: "\e051";
}
.soc-pinterest:before {
  content: "\e043";
}
.soc-yelp:before {
  content: "\e047";
}
.headersocial a {
  color: #146fb8 !important;
  display: inline-block;
  width: 50px;
  height: 50px;
  background: #faed00;
  text-align: center;
  border-radius: 50%;
  font-size: 25px;
}
.headersocial a:hover {
  opacity: 0.7;
}
.headersocial a:before {
  line-height: 48px;
}
body {
  font-family: "gothambook";
  font-weight: normal !important;
  color: #000000;
}
header {
  z-index: 90;
}
.headerhigh {
  position: relative;
  width: 100%;
  height: 155px;
  z-index: -200;
}
img {
  max-width: 100%;
  height: auto;
}
.whiteborderimg {
  border: 5px solid #ffffff;
}
.container {
  max-width: 1790px;
}
.clearall {
  clear: both;
}
.lastitem {
  margin-bottom: 0 !important;
}
dl,
ol,
ul {
  margin-bottom: 0;
  text-transform: uppercase;
}
h1,
h2,
h3,
h5,
h6 {
  font-family: "hogfish_demoregular";
  font-weight: normal;
  text-transform: uppercase;
}
h1 {
  font-size: 72px;
  line-height: 82px;
}
h2 {
  font-size: 48px;
  line-height: 66px;
  margin-bottom: 35px;
}
h3 {
  font-size: 36px;
  line-height: 54px;
}
h4 {
  font-size: 24px;
  line-height: 36px;
  font-family: "gothambook";
  font-weight: normal;
}
h5 {
  font-size: 24px;
  line-height: 36px;
}
h6 {
  font-size: 18px;
  line-height: 26px;
}
p {
  font-size: 18px;
  line-height: 30px;
  margin-bottom: 35px;
}
p.biggertxt {
  font-size: 24px;
  line-height: 35px;
}
p strong {
  color: #146fb8;
  font-family: "gothamblack";
  font-weight: normal;
}
.blogarticle p {
  margin-bottom: 35px;
}
.text-white p strong {
  color: #ffffff;
}
section a {
  color: #146fb8;
  text-decoration: underline;
  font-family: "gothamblack";
}
a:hover {
  -webkit-transition: all 0.2s ease-out 0s;
  transition: all 0.2s ease-out 0s;
}
.whitetxt {
  color: #ffffff;
}
.yellowtxt {
  color: #faed00;
}
.bluetxt {
  color: #146fb8;
}
.text-blue {
  color: #146fb8 !important;
}
.greentxt {
  color: #4faf88;
}
.bg-primary {
  background: url(../images/bluetexture.png);
  background-color: #146fb8 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
}
.bg-secondary {
  background: url(../images/greentexture.png);
  background-color: #4faf88 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
}
.bg-tertiary {
  background: url(../images/yellowtexture.png);
  background-color: #faed00 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
}
.page-section {
  padding-top: 80px;
  padding-bottom: 80px;
  overflow: hidden;
}
.page-section-giftcards {
  padding-top: 80px;
  padding-bottom: 0px;
  overflow: hidden;
}
.nopaddtopsec {
  padding-top: 0 !important;
}
.nopaddbottsec {
  padding-bottom: 0 !important;
}
.homeglobelft {
  width: 56%;
  padding-right: 15px;
}
.homeglobergt {
  width: 43%;
  padding-left: 15px;
}
.alignmidd {
  display: inline-block;
  vertical-align: middle;
}
.middlewdt {
  max-width: 1660px;
}
.smallwdt {
  max-width: 1390px;
  margin: 0 auto;
}
.smallestwdt {
  max-width: 1290px;
  margin: 0 auto;
}
.container.smallestwdt.wrtscalendarwrap {
  width: 100%;
  max-width: 100%;
}
.wrtscalendarwrap img {
  display: inline-block;
  margin: 0 auto 50px;
}
.headertop h6,
.headertop a,
.headertop .headersocial {
  display: inline-block;
  vertical-align: middle;
}
.headertop span a {
  vertical-align: top;
  color: #faed00;
}
.headertop h6 {
  color: #ffffff;
  padding-right: 15px;
  margin-bottom: 0;
}
.wrtsbtn {
  display: block;
  max-width: 200px;
  text-align: center;
  font-family: "hogfish_demoregular";
  font-size: 18px;
  padding: 19px 0;
  border-radius: 50px;
  font-weight: normal !important;
  margin: 0 auto;
  text-transform: uppercase;
  text-decoration: none;
}
.inlinebtn {
  display: inline-block;
  max-width: 100%;
  padding-left: 60px !important;
  padding-right: 60px !important;
  font-size: 24px;
}
.biggerbtn {
  max-width: 250px;
}
.fullbtn {
  max-width: 100%;
}
.yellowbtn {
  color: #146fb8 !important;
  background: #faed00;
}
.yellowbtn:hover {
  color: #ffffff !important;
  background: #3289cf;
  text-decoration: none;
}
.bluebtn {
  color: #ffffff !important;
  background: #146fb8;
}
.bluebtn:hover {
  color: #146fb8 !important;
  background: #faed00;
  text-decoration: none;
}
header .yellowbtn {
  padding: 13px 22px;
  font-size: 16px;
  margin-right: 8px;
}
.navbar {
  background: #146fb8;
}
.navitems li {
  margin-left: 0;
}
.navitems li a {
  font: 17px "gothamblack";
  text-transform: capitalize;
  padding: 0 0 30px 30px !important;
  color: #ffffff;
}
.navitems li.active a,
.navitems li a:hover,
.navitems li a:active {
  color: #faed00 !important;
}
/**/

/*new navigation*/
header {
  position: fixed;
  width: 100%;
}
.navbar {
  position: relative;
  display: block;
  float: left;
  width: 100%;
}
.navitems {
  float: right;
  margin-right: 60px;
}
.navitems ul {
  padding: 0;
}
.navitems ul li {
  display: inline-block;
  list-style-type: none;
  position: relative;
}
.navitems ul ul {
  background: rgba(20, 111, 184, 0.9);
  min-width: 310px;
  max-width: 310px;
  padding: 20px 30px;
  margin-top: 25px;
  left: 0;
  position: absolute;
  border-radius: 0.5rem;
}
.navitems ul li ul li {
  clear: both;
  width: 100%;
}
.navitems ul ul a {
  padding: 0 !important;
  text-decoration: none;
}
.caret {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #fff;
  margin-top: 11px;
  display: block;
  float: right;
  margin-left: 6px;
}
/*end newnavigagtion*/

.navbar-brand {
  float: left;
  padding: 0;
}
.headertop,
.headerrgt {
  float: right;
}
.headertop {
  margin-bottom: 35px;
  text-align: right;
}
.homethreeboxes {
  text-align: center;
}
.flexwrap {
  display: flex;
  width: 100%;
  justify-content: center;
}
.flexbox {
  flex: 1;
  position: relative;
}
.hbox {
  max-width: 420px;
  padding-bottom: 80px;
}
.hbox img {
  margin-bottom: 45px;
}
.hbox h3 {
  margin-bottom: 20px;
}
.hbox:nth-of-type(2) {
  margin-left: 50px;
  margin-right: 50px;
}
.hbox p {
  margin-bottom: 25px;
}
.hbox .yellowbtn {
  position: absolute;
  bottom: 0;
  width: 200px;
  left: 50%;
  margin-left: -100px;
}
.hbox:hover h3 {
  color: yellow;
}
.homethreeboxes .hbox:hover h3 {
  color: #fff !important;
}
.centersec {
  text-align: center;
}
#abouthome {
  text-align: center;
  padding-top: 50px;
  padding-bottom: 50px;
}
#abouthome .wrtsbtn {
  display: inline-block;
  margin-left: 12px;
  margin-right: 12px;
  width: 200px;
}
#abouthome img.line {
  margin-top: 60px;
  margin-bottom: 60px;
}
.abouthomeapp h6 {
  margin-bottom: 0;
  margin-right: 20px;
}
.abouthomeapp h6,
.abouthomeapp img {
  display: inline-block;
  vertical-align: middle;
}
.abouthomeapp img {
  width: 160px;
}
.testimsec {
  text-align: center;
}
.testimsec h2 {
  margin-bottom: 20px;
}
.carousel {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
}
.carousel .item {
  text-align: center;
  overflow: hidden;
  min-height: 280px;
}
.carousel .testimonial {
  font: 36px "gothambook";
  font-style: italic;
}
.carousel .overview {
  text-align: center;
  font: 24px "hogfish_demoregular";
}
.carousel .carousel-indicators {
  bottom: 15px;
}
.carousel-indicators li {
  width: 11px;
  height: 11px;
  margin: 1px 5px;
  border-radius: 50%;
  padding: 0;
  border: 3px solid #ffffff !important;
  background: transparent !important;
  opacity: 1;
}
.carousel-indicators li.active {
  border: none;
  background: #ffffff !important;
}
.testimsec {
  padding-bottom: 0 !important;
}
.staffttl {
  margin-bottom: 70px;
}
.staffmember {
  display: inline-block;
  margin-left: 17px;
  margin-right: 17px;
  vertical-align: top;
}
.staffmember img {
  margin-bottom: 50px;
}
.staffmember p {
  padding-top: 10px;
  margin-bottom: 0;
}
.innerbanner {
  background: url(../images/bannertexture.png);
  background-color: #146fb8 !important;
  background-repeat: repeat-x;
  background-position: center top;
  background-size: contain;
  text-align: center;
  color: #faed00;
}
.innerbanner h1 {
  padding-top: 110px;
  padding-bottom: 110px;
  margin: 0;
}
.smallestwdt .carousel {
  max-width: 100%;
}
#aboutsliderdiv #myCarousel {
  margin-top: 70px;
}
.startxt,
section ul {
  padding: 0 0 0 30px;
  position: relative;
}
section li {
  font-size: 24px;
  font-weight: normal;
  margin-bottom: 5;
  text-transform: initial;
}
.startxt li {
  font-size: 24px;
  font-family: "hogfish_demoregular";
  text-transform: uppercase;
  font-weight: normal;
  list-style: none;
  margin-bottom: 5px;
}
.startxt li:before {
  content: "";
  position: absolute;
  width: 20px;
  height: 19px;
  background: url(../images/txtstar.png) center center no-repeat;
  background-size: contain;
  display: inline-block;
  vertical-align: top;
  margin-top: 8px;
  left: 0;
}
.bg-tertiary .startxt li:before {
  background: url(../images/txtbluestar.png) center center no-repeat;
}
.startxt.normalfontlist li {
  font: 24px/36px "gothambook";
  margin-bottom: 24px;
}
.startxt.normalfontlist li:last-of-type {
  margin-bottom: 0;
}
.startxt.normalfontlist li span {
  font-family: "gothamblack";
}
.startxt.smallfontlist {
  padding-left: 20px;
}
.startxt.smallfontlist li {
  font-size: 18px;
  line-height: 26px;
  margin-bottom: 15px !important;
  font-family: "gothamblack";
}
.startxt.smallfontlist li:before {
  width: 8px;
  height: 8px;
  background: #faed00;
  border-radius: 50%;
  margin-top: 10px;
}
#aboutincludes h2 {
  margin-bottom: 15px;
}
.halfdiv {
  width: 45%;
}
.halflft {
  float: left;
}
.halfrgt {
  float: right;
}
#aboutincludes .carousel {
  border: 5px solid #ffffff;
  max-width: 780px;
}
#aboutincludes .carousel-indicators {
  left: inherit;
  margin: 0 30px 10px 0;
}
#aboutincludes .carousel-indicators li.active {
  background: #faed00 !important;
  border-color: #faed00 !important;
}
.videoWrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  padding-top: 25px;
  height: 0;
}
.videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.mapwrap {
  background: url(../images/mapimg.webp) center center no-repeat;
  text-align: center;
  padding-top: 50px;
  padding-bottom: 65px;
}
.mapwrap .bluebtn {
  color: #faed00 !important;
}
.mapwrap .bluebtn:hover {
  color: #146fb8 !important;
}
.mapwrap img {
  margin-top: 140px;
  margin-bottom: 140px;
}
.map_bg {
  width: 100%;
}
.whyheading {
  margin-top: 80px;
}
.yellowfull {
  width: 100%;
  background: url(../images/yellowtexture.png);
  background-color: #f8eb00 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
  text-align: center;
  padding-left: 15px;
  padding-right: 15px;
}
.yellowfull h2 {
  padding-top: 80px;
  padding-bottom: 80px;
  margin: 0;
}
.whywelist {
  text-align: center;
}
.flexwrap {
  align-items: center;
}
img.starlft {
  border: none;
  position: absolute;
  left: -45px;
  top: 50%;
  margin-top: -45px;
}
img.starrgt {
  border: none;
  position: absolute;
  right: -45px;
  top: 50%;
  margin-top: -45px;
}
.plist {
  line-height: 45px;
}
.whywewhitelist .whylistrgt {
  text-align: center;
}
.whywelist.page-section {
  padding-top: 135px;
  padding-bottom: 135px;
}
#whyposter {
  text-align: center;
}
#whyposter h4 {
  margin-top: 50px;
  margin-bottom: 55px;
}
.whyposterimg {
  margin-top: 80px;
  margin-bottom: 90px;
}
.equipstarimgrgt {
  max-width: 780px;
  position: relative;
  float: right;
}
.equipstarimglft {
  max-width: 780px;
  position: relative;
  float: left;
}
.yelpcontainer {
  max-width: 1180px;
}
.yelplftcol,
.yelprgtcol {
  width: 48.3%;
  display: inline-block;
  vertical-align: top;
}
.yelpwrap {
  margin-bottom: 11%;
}
.yelprgtcol {
  margin-left: 2.5%;
}
.yelpcontainer h2 {
  margin-bottom: 70px;
  text-align: center;
}
.moreyelpbtn {
  margin-top: 80px;
}
#locgreen {
  text-align: center;
  padding-top: 70px;
  padding-bottom: 70px;
}
#tabs {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin-top: 40px;
  text-align: center;
}
#tabs li {
  width: 100%;
  max-width: 300px;
  text-align: center;
  font: 18px/24px "hogfish_demoregular";
  text-transform: uppercase;
  background: #146fb8;
  color: #faed00;
  cursor: pointer;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 17px 10px;
}
#tabs li.inactive {
  background: #faed00;
  color: #146fb8;
}
#tabs li:hover {
  background: #faed00;
  color: #146fb8;
}
#tabs li.inactive:hover {
  background: #146fb8;
  color: #faed00;
}
#tab2 {
  margin-left: 10px;
  margin-right: 10px;
}
#tab3 {
  margin-right: 10px;
}
.tabsline {
  margin-top: 50px;
  margin-bottom: 70px;
}
.locationwrap {
  position: relative;
  padding: 0 10px 65px;
  width: 33.3%;
  margin-bottom: 100px;
}
.locationwrap p {
  line-height: 25px;
  margin-bottom: 15px;
}
.locationwrap h3 {
  color: #146fb8;
  margin-top: 55px;
  margin-bottom: 30px;
}
.greenlinksloc {
  margin-top: 20px;
  margin-bottom: 35px;
}
.greenlinksloc h4 a {
  font-family: "hogfish_demoregular";
  text-transform: uppercase;
  text-decoration: underline;
  color: #4faf88;
}
.greenlinksloc h4 a:hover {
  color: #146fb8;
}
.tabcontent {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
}
.locationwrap .wrtsbtn {
  position: absolute;
  bottom: 0;
  width: 260px;
  left: 50%;
  margin-left: -100px;
}
.usaopenwrap .locationwrap,
.usasoonwrap .locationwrap,
.internatwrap .locationwrap,
.intersoonnatwrap .locationwrap {
  display: none;
}
.btnlineloc {
  margin-top: -15px;
}
.btnlineloc .wrtsbtn {
  margin-top: 90px;
}
.nonprofline {
  margin-top: 90px;
  margin-bottom: 90px;
}
.openplft {
  width: 50%;
  float: left;
  margin-bottom: 60px;
}
.openprgt {
  width: 46%;
  float: right;
}
p.pleasecallp {
  margin-top: 25px;
  margin-bottom: 20px;
  padding-right: 300px;
  text-transform: initial;
}
.twothirdcol {
  width: 70%;
  float: left;
}
.onethirdcol {
  width: 30%;
  float: right;
}
.starlistspacing li {
  margin-bottom: 25px;
}
.openphours .onethirdcol h6 {
  line-height: 36px;
  text-align: center;
  text-decoration: underline;
  margin-bottom: 35px;
}
.openphours .onethirdcol a {
  color: #146fb8 !important;
  background: #faed00;
  display: block;
  max-width: 100%;
  text-align: center;
  font-family: "hogfish_demoregular";
  font-size: 18px;
  padding: 19px 0;
  border-radius: 50px;
  font-weight: normal !important;
  margin: 0 auto 25px auto;
  text-transform: uppercase;
  text-decoration: none;
}
.openphours .openplayheal {
  margin: 0 !important;
  display: block;
}
.healwrap {
  margin-bottom: 25px;
}
#openpbasic .flexbox {
  text-align: left;
}
#openmember {
  text-align: center;
}
#openmember h5 {
  margin-bottom: 26px;
}
.greenlft {
  width: 19%;
  margin-right: 2%;
}
.greenrgt {
  width: 78%;
}
.greenlft,
.greenrgt {
  display: inline-block;
  vertical-align: top;
  text-align: left;
}
.respitesec .openplft img {
  margin-bottom: 60px;
}
.addinfo {
  margin-bottom: 15px;
}
#pricingsec .twothirdcol {
  padding-right: 200px;
}
#pricingsec .onethirdcol {
  max-width: 320px;
}
#bookparty h2 {
  margin: 0;
}
#bookparty h2,
#bookparty .bookawrap {
  display: inline-block;
  width: 49%;
  vertical-align: middle;
}
#bookparty .bookawrap {
  text-align: right;
}
#bookparty a {
  font-size: 36px;
  padding: 8px 0;
  display: inherit;
  width: 92%;
}
#bookparty img {
  margin-top: 90px;
}
#toplftcarous .carousel {
  max-width: 630px;
}
#toplftcarous .carousel-indicators {
  top: 30px;
  left: 30px;
  right: inherit;
  margin: 0;
}
.bdaybtns {
  max-width: 320px;
  width: 100%;
  margin: 0 auto;
}
.bdaybtns a {
  margin-bottom: 15px;
}
#pricingsec .starlistspacing {
  padding-right: 80px;
}
.bdayintro p {
  margin-top: 35px;
  margin-bottom: 70px;
}
.linemar {
  margin-top: 90px;
  margin-bottom: 90px;
}
.packagesdiv {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  text-align: left;
}
.pricingbox {
  width: 47.5%;
  padding: 40px 40px 85px;
  border-radius: 20px;
  margin: 0 auto;
}
.pricingbox h2 {
  margin-bottom: 10px;
}
.pricingbox:first-of-type,
.pricingbox:nth-of-type(2) {
  margin-bottom: 60px;
}
.pricingbox img {
  margin-top: 20px;
  margin-bottom: 35px;
}
.bdaytxtadjust h5 {
  line-height: 50px;
}
.bdaytxtadjust h5.yellowtxt {
  margin-top: 30px;
  margin-bottom: 0;
}
.bdaytxtadjust ul {
  margin-top: 30px;
}
.bluelist li {
  color: #146fb8;
}
.specialtripsbtn h4 {
  margin-bottom: 85px;
}
.notoppaddsec {
  padding-top: 0 !important;
}
.wheelsline {
  margin-top: 90px;
}
.packagesthree ul li {
  font-size: 18px;
}
.packagesthree ul li:before {
  margin-top: 3px;
}
.packagesthree .pricingbox {
  width: 30%;
  padding: 30px 20px 20px 25px;
}
.packagesthree .pricingbox {
  margin-bottom: 60px;
}
.packagesthree h5 {
  margin-bottom: 30px;
}
.resp-iframe iframe {
  max-width: none !important;
  width: 100% !important;
}
.resp-container {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%;
}
.resp-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.schoolsline {
  margin-top: 85px;
  margin-bottom: 65px;
}
.threesessions {
  margin-bottom: 70px;
}
.threesessions h5,
.threesessions span {
  color: #4faf88;
  display: inline-block;
  vertical-align: top;
}
.threesessions span {
  font-size: 27px;
  margin-left: 17px;
  margin-right: 17px;
}
.schoolspricing {
  text-align: center;
  padding: 55px 15px !important;
  margin-top: 15px;
  margin-bottom: 0 !important;
}
.schoolspricing h3 {
  line-height: 55px;
}
#accordionfaq {
  text-align: left;
}
#accordionfaq .card-body p {
  font-size: 24px;
}
.whywelist .whylistlft h4 {
  margin-bottom: 35px;
}
@media (max-width: 767px) {
  #mainNav.fixedd .navitems {
    position: fixed;
    top: 47px;
    background: #3289cf;
  }
  #mainNav.fixedd .headerrgt {
    position: fixed;
    top: 0;
    left: 0;
  }
  #accordionfaq .card-body p {
    font-size: 15px;
    line-height: 20px;
  }
  .whywelist .whylistlft h4 {
    margin-bottom: 20px;
  }
}
.card-header {
  border: none;
  background: none;
  padding: 0;
}
.card-header button {
  font-size: 24px;
  color: #146fb8;
  text-transform: uppercase;
  width: 100%;
  text-align: left;
  border-radius: 30px;
  padding: 12px 8px 12px 35px;
  text-decoration: none !important;
  background: #faed00 url(../images/accup.png) center right 12px no-repeat;
}
.card-header button.collapsed {
  color: #ffffff;
  background: #146fb8 url(../images/accdown.png) center right 12px no-repeat;
}
.accordion > .card .card-header {
  margin-bottom: 0;
}
.accordion > .card {
  border: none;
  margin-bottom: 10px;
}
.card-body {
  font-size: 24px;
  padding: 40px 0 20px;
}
.card-body ul,
.card-body ol {
  margin-top: 25px;
  margin-bottom: 25px;
}
.card-body ul li,
.card-body ol li {
  text-transform: initial;
}
.recommimg {
  width: 30%;
  height: auto;
  margin-bottom: 70px;
  background: url(../images/recommlogos/recommframe.png) center center no-repeat;
  background-size: contain;
  padding-top: 70px;
  padding-bottom: 70px;
  display: inline-block;
}
.recommimg:nth-of-type(3n + 2) {
  margin-left: 4.4%;
  margin-right: 4.4%;
}
.conthours {
  margin-top: 40px;
  margin-bottom: 35px;
}
#contactsec .toup {
  text-align: left;
  padding-left: 35px;
}
#contactsec .todwn {
  padding-right: 35px;
}
.contline {
  margin-top: 45px;
  margin-bottom: 35px;
  clear: both;
}
.blogwrapper {
  display: flex;
}
.bloglft {
  flex: 1;
}
.blogsidebar {
  flex: 0 0 500px;
  color: #ffffff;
}
.sidewidget {
  width: 100%;
  max-width: 400px;
  float: right;
  background: url(../images/bluetexture.png);
  background-color: #146fb8 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
  padding: 50px;
  border-radius: 20px;
  margin-bottom: 50px;
}
.widgetnopadd {
  padding: 0;
}
.blogsidebar h3 {
  color: #faed00;
  margin-bottom: 40px;
}
.blogsidebar h5 a {
  color: #ffffff;
  font-family: "hogfish_demoregular";
  text-decoration: none;
}
.blogsidebar h5 a:hover {
  color: #faed00;
}
.blogimg {
  width: 40%;
  float: left;
}
.bloxexc {
  width: 58%;
  float: right;
}
.bloxexc h2 {
  color: #146fb8;
}
.bloxexc h5 {
  margin-bottom: 35px;
  color: #4faf88;
}
.bloxexc p {
  margin-bottom: 35px;
}
.blogline {
  margin-top: 65px;
  margin-bottom: 65px;
}
.singblogline {
  display: none;
}
.singlelastlnk {
  font-family: "gothamblack";
  color: #146fb8;
}
.bloglftwrap {
  display: flex;
  flex-direction: column;
}
.sidebarimg {
  text-align: center;
}
.sidebarimg a.inlinebtn {
  width: 320px;
  margin-top: 50px;
  margin-bottom: 50px;
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.reswrap .blogimg {
  width: 36%;
}
.reswrap .bloxexc {
  width: 62%;
}
#purposeintro h5 {
  max-width: 900px;
  margin: 0 auto;
}
.pricingbox {
  position: relative;
}
.purposeboxes p {
  font-family: "gothamblack";
  text-align: justify;
}
.purposelft {
  width: 65%;
  float: left;
}
.purpkid {
  float: right;
  margin-top: -10px !important;
}
.purpstar {
  position: absolute;
  bottom: 0;
  right: 30px;
}
.purposeboxes .pricingbox {
  padding: 60px 30px 70px 50px !important;
  margin-bottom: 60px;
}
.purposeboxes .pricingbox h2 {
  margin-bottom: 35px;
}
.lastonebox {
  margin: 0 auto;
}
.purposeline {
  margin-top: 30px;
  margin-bottom: 110px;
}
.purposeposter a {
  margin-top: 90px;
}
.wrtscalendarwrap img {
  display: none;
  border: 5px solid #ffffff;
  margin-bottom: 50px;
}
footer {
  background: url(../images/bluetexture.png);
  background-color: #146fb8 !important;
  background-repeat: repeat-x;
  background-position: center bottom;
  background-size: contain;
  text-align: center;
  color: #ffffff;
  padding-top: 80px;
  padding-bottom: 50px;
}
.footaddress {
  margin-bottom: 70px;
}
.footer-nav a {
  font: 18px "gothamblack";
  color: #ffffff;
  padding-left: 12px;
  padding-right: 12px;
}
.footsoc {
  margin-top: 60px;
  margin-bottom: 30px;
}
.footer-nav a:hover {
  color: #faed00 !important;
  text-decoration: none;
}
.footinst {
  margin-top: 30px;
  margin-bottom: 50px;
}
.footinst a:hover {
  color: #ffffff !important;
  text-decoration: none;
}
.videotxt {
  position: absolute;
  z-index: 50;
  text-align: center;
  color: #ffffff;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 67%;
  height: 30%;
  margin: auto;
}
.videotxt h1 {
  margin-top: 20px;
  margin-bottom: 50px;
  line-height: 90px;
}
.videoopacity {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(20, 111, 184, 0.3);
  z-index: 40;
}

.blogslider {
  text-align: center;
  padding-bottom: 125px;
}
#myCarousel8.carousel {
  max-width: 1400px;
  padding-left: 15px;
  padding-right: 15px;
  margin-top: 80px;
}
.blogslider .featslider {
  width: 40%;
  float: left;
  position: relative;
}
.blogslider .carousel-indicators {
  right: inherit;
  bottom: -35px;
}
.blogslider .carousel-inner {
  overflow: visible;
}
.bslcont {
  float: right;
  width: 57%;
}
.blogslider h3 {
  margin-bottom: 30px;
}
.blogslider p {
  margin-bottom: 30px;
}
.dateslider {
  position: absolute;
  background: #146fb8;
  top: 20px;
  right: 20px;
  width: 90px;
  height: 88px;
  border-radius: 50%;
}
.bslday,
.bslmonth {
  color: #faed00;
  font-family: "hogfish_demoregular";
  display: block;
}
.bslday {
  font-size: 36px;
  margin-top: 2px;
}
.bslmonth {
  font-size: 18px;
}

#homeeventswrap {
  text-align: center;
}
#homeeventswrap h2 {
  margin-bottom: 80px;
}
#homeeventswrap h2 a {
  font-family: "hogfish_demoregular";
  color: #faed00;
  text-decoration: underline;
}
#homeeventswrap h2 a:hover {
  color: #146fb8;
}

.homethreeev {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}
.homeeventbox {
  width: 30%;
  padding-bottom: 30px;
  position: relative;
}
.homeeventbox:nth-of-type(2) {
  margin-left: 3%;
  margin-right: 3%;
}
.homeeventbox img {
  margin-bottom: 35px;
}
.homeeventbox h5 {
  margin-bottom: 20px;
  margin-top: 25px;
}
.homeeventbox h5 a {
  font-family: "hogfish_demoregular";
  text-decoration: none;
}
.homeeventbox p {
  margin-bottom: 0;
}
.homeevdate {
  position: absolute;
  bottom: 0;
  text-align: center;
  width: 100%;
}
.oopstxt {
  width: 65%;
}
.oopsimg {
  width: 34%;
  text-align: center;
}
.oopstxt,
.oopsimg {
  display: inline-block;
  vertical-align: top;
}
.oopstxt h2 {
  font-size: 114px;
}
.oopstxt p {
  font: 72px "hogfish_demoregular";
}
.oopsline {
  margin-top: -20px;
}
#oopseventswrap {
  text-align: center;
  padding-top: 65px;
}
#oopseventswrap .container {
  margin-top: 75px;
}
#oopseventswrap .homeeventbox:nth-of-type(2) {
  margin-left: 3%;
  margin-right: 0;
}
#contformsec {
  padding-top: 60px;
  padding-bottom: 60px;
}

#contformsec h2 {
  margin-bottom: 55px;
}
#contactform {
  max-width: 1260px;
}
#contactform input {
  width: 49%;
  float: left;
  margin-bottom: 20px;
  height: 70px;
}
#contactform input:nth-of-type(even) {
  float: right;
}
#contactform textarea {
  width: 100%;
  min-height: 200px;
  padding-top: 20px;
}
#contactform input,
#contactform textarea {
  border: none;
  border-radius: 30px;
  padding-left: 40px;
}
#contactform input#submit {
  width: 100%;
  max-width: 320px;
  float: none;
  padding: 0;
  color: #146fb8 !important;
  background: #faed00;
  font: 18px "hogfish_demoregular";
  margin: 50px auto 0;
}
#contactform input#submit:hover {
  color: #ffffff !important;
  background: #146fb8;
}

.wheeltxt {
  width: 100%;
  text-align: center;
  bottom: -80px;
  position: absolute;
}
.contapp {
  text-align: center;
}
.contapp h6 {
  font-size: 24px;
}
.contapp .appmob {
  margin-left: 25px;
}

@media (max-width: 1650px) {
  .headerhigh {
    height: 188px;
  }
  h2 {
    font-size: 40px;
    line-height: 56px;
  }
  h4 {
    font-size: 20px;
    line-height: 30px;
  }
  .navitems {
    margin-right: 0;
    width: 100%;
    text-align: center;
  }
  .navitems ul ul {
    text-align: left;
  }
  .navitems li a {
    padding: 0 13px 30px 13px !important;
  }
  .caret {
    margin-left: -6px;
  }
  .headertop h6 {
    display: block;
    margin-bottom: 15px;
    padding-right: 0;
  }
  .abouthomeapp h6 {
    display: block;
    margin-bottom: 20px;
    margin-right: 0;
  }
  #abouthome img.line {
    margin-top: 40px;
    margin-bottom: 40px;
  }
  .staffmember {
    width: 25%;
  }
  .staffmember span {
    display: block;
  }
  .halfdiv {
    width: 47%;
  }
  .whylistlft {
    padding-right: 60px;
  }
  .plist {
    line-height: 38px;
  }
  .whywelist.page-section {
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .whyposterimg {
    margin-top: 50px;
    margin-bottom: 50px;
  }
  .specialtripsbtn h4 {
    margin-bottom: 60px;
  }
  .wheelsline {
    margin-top: 100px;
  }
  .card-body,
  .card-body ul li {
    font-size: 20px;
    line-height: 30px;
  }
  .card-body ul li {
    margin-bottom: 0;
  }
  .recommimg {
    margin-bottom: 30px;
  }
  #contactsec .toup {
    padding-left: 10px;
  }
  #contactsec .todwn {
    padding-right: 10px;
  }
  .blogsidebar {
    flex: 0 0 450px;
  }
  .bloxexc h5 {
    margin-bottom: 15px;
  }
  .bloxexc h2 {
    margin-bottom: 20px;
  }
  .purposeline {
    margin-bottom: 70px;
  }
  .purposeposter a {
    margin-top: 50px;
  }
  .innerbanner.calbanner h1 {
    font-size: 50px;
  }
  .videotxt {
    padding-top: 80px;
  }
  .videotxt h1 {
    font-size: 45px;
    line-height: 60px;
    margin-bottom: 25px;
  }
  .oopstxt p {
    font-size: 67px;
  }
}

@media (max-width: 1199px) {
  .res {
    background: purple;
    width: 25px;
    height: 12px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  h1 {
    font-size: 60px;
    line-height: 72px;
  }
  h2 {
    font-size: 33px;
    line-height: 46px;
    margin-bottom: 25px;
  }
  h3 {
    font-size: 28px;
    line-height: 36px;
  }
  p {
    margin-bottom: 30px;
  }
  p.biggertxt {
    font-size: 19px;
    line-height: 26px;
  }
  .navitems li a {
    padding-left: 7px !important;
    padding-right: 7px !important;
    font-size: 15px;
  }
  .caret {
    margin-left: -3px;
  }
  .carousel .item {
    min-height: 220px;
  }
  .carousel .testimonial {
    font-size: 28px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .staffmember img {
    margin-bottom: 25px;
  }
  .innerbanner h1 {
    padding-top: 80px;
    padding-bottom: 80px;
  }
  .startxt li {
    font-size: 20px;
  }
  .startxt li::before {
    margin-top: 4px;
  }
  .mapwrap img {
    margin-top: 100px;
    margin-bottom: 100px;
  }
  .yellowfull h2 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  #whyposter h4 {
    margin-top: 30px;
    margin-bottom: 55px;
  }
  .locationwrap h3 {
    margin-top: 35px;
  }
  .locationwrap {
    margin-bottom: 70px;
  }
  .btnlineloc .wrtsbtn {
    margin-top: 60px;
  }
  .whyheading {
    margin-top: 50px;
  }
  .nonprofline {
    margin-top: 50px;
    margin-bottom: 60px;
  }
  .twothirdcol,
  .onethirdcol {
    width: 50%;
  }
  .startxt.normalfontlist li {
    font-size: 18px;
    line-height: 28px;
    margin-bottom: 15px;
  }
  #openpbasic .flexbox h2 {
    margin-bottom: 25px;
  }
  #openmember h5 {
    margin-bottom: 15px;
  }
  .respitesec .openplft img {
    margin-bottom: 30px;
  }
  #pricingsec .twothirdcol {
    padding-right: 0;
  }
  #pricingsec .onethirdcol {
    max-width: 40%;
  }
  #pricingsec .twothirdcol {
    padding-right: 100px;
  }
  #bookparty h2 {
    text-align: center;
  }
  #bookparty a {
    font-size: 26px;
    width: 100%;
  }
  .pricingbox {
    padding: 25px 25px 45px;
  }
  .bdaytxtadjust h5 {
    line-height: 32px;
  }
  .bdaytxtadjust img {
    margin-top: 25px;
  }
  .wheelsline {
    margin-top: 50px;
  }
  .packagesthree .pricingbox {
    width: 32%;
  }
  .staffttl {
    margin-bottom: 50px;
  }
  .schoolsline {
    margin-top: 50px;
  }
  .threesessions {
    margin-bottom: 35px;
  }
  .card-body {
    padding-top: 25px;
  }
  .recommimg {
    margin-bottom: 0;
    padding-top: 5%;
    padding-bottom: 5%;
  }
  .conthours {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .blogsidebar {
    flex: 0 0 370px;
  }
  .sidewidget {
    max-width: 350px;
  }
  .bloxexc h5 {
    font-size: 17px;
  }
  .bloxexc p {
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 15px;
  }
  .bloxexc h2 {
    font-size: 26px;
    line-height: 37px;
    margin-bottom: 10px;
  }
  .blogline {
    margin-top: 35px;
    margin-bottom: 35px;
  }
  .sidewidget h5 {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 0;
  }
  .blogsidebar h3 {
    margin-bottom: 16px;
  }
  .purposeboxes .pricingbox {
    padding-left: 30px !important;
  }
  .purposelft {
    width: 58%;
  }
  .purposeboxes p {
    text-align: left;
  }
  .wrtscalendarwrap img {
    margin-bottom: 25px;
  }
  .innerbanner.calbanner h1 {
    font-size: 45px;
  }
  footer {
    padding-top: 50px;
  }
  .footer-nav {
    max-width: 70%;
    margin: 0 auto;
  }
  .videotxt {
    width: 90%;
    padding-top: 0;
    height: 45%;
    height: 270px;
  }
  .blogslider h3 {
    margin-bottom: 20px;
  }
  .blogslider p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 20px;
  }
  #myCarousel8.carousel {
    margin-top: 45px;
  }
  .homeeventbox img {
    margin-bottom: 20px;
  }
  .homeeventbox h5 {
    margin-bottom: 10px;
  }
  #homeeventswrap h2 {
    margin-bottom: 55px;
  }
  .oopstxt h2 {
    font-size: 80px;
  }
  .oopstxt p {
    font-size: 57px;
    line-height: 70px;
  }
  .oopstxt h2 {
    margin-bottom: 35px;
  }
  #oopseventswrap .container {
    margin-top: 50px;
  }
  #oopseventswrap {
    padding-top: 35px;
  }
  .footaddress {
    margin-bottom: 25px;
  }
  p.pleasecallp {
    padding-right: 0;
  }
  .staffmember {
    width: 30%;
  }
}

@media (min-width: 992px) {
  .navitems ul ul {
    max-height: 0;
    overflow: hidden;
    padding: 0;
    border: none;
  }
  .navitems ul li:hover > ul,
  .navitems ul li ul:hover {
    transition: max-height 0.5s ease-out;
    max-height: 500px;
    padding: 20px 30px;
    border: 0.125rem solid rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 991px) {
  .res {
    background: blue;
  }
  h1 {
    font-size: 50px;
    line-height: 60px;
  }
  h2 {
    font-size: 30px;
    line-height: 42px;
  }
  p,
  p.biggertxt,
  .startxt.normalfontlist li,
  .startxt.smallfontlist li,
  #pricingsec .startxt li,
  section li {
    font-size: 16px;
    line-height: 24px;
  }
  .wrtsbtn {
    padding: 12px 0;
  }
  /**/
  #mainNav {
    padding: 20px 0 0 0;
  }
  .mobmenu {
    clear: both;
    float: left;
  }
  .navbar-toggler {
    background: transparent !important;
    color: #ffffff !important;
    font-size: 18px !important;
  }
  .navbar-toggler img {
    max-height: 20px;
    margin-right: 6px;
    margin-top: -4px;
  }
  .navitems ul li {
    cursor: pointer;
  }
  .navitems ul ul {
    display: none;
    padding: 3px 20px 10px 13px;
    margin-top: 10px;
    left: 15px;
    position: relative;
    border: none;
  }
  .navitems ul ul a {
    font-family: "gothambook" !important;
    font-size: 14px;
    display: block;
  }
  .mobilenav {
    margin-top: 10px;
  }
  .headerrgt {
    width: 100%;
    background: #3289cf;
  }
  .navbar-brand {
    margin-left: 20px;
  }
  .headertop {
    margin-right: 20px;
  }
  .navitems li {
    margin-bottom: 10px;
    padding-bottom: 10px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0 !important;
  }
  .navitems .nav-dropdown li:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0 !important;
    border: none;
  }
  #mainNav .navitems {
    padding-left: 15px;
    padding-right: 15px;
  }
  .navitems {
    display: none;
  }
  .navitems ul li {
    display: block;
    text-align: left;
  }
  .headertop {
    margin-bottom: 25px;
    margin-top: 5px;
  }
  .headertop h6 {
    font-size: 15px;
  }
  .hbox img {
    margin-bottom: 25px;
  }
  .hbox p {
    margin-bottom: 0px;
  }
  .hbox:nth-of-type(2) {
    margin-left: 25px;
    margin-right: 25px;
  }
  .page-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .homeglobelft,
  .homeglobergt {
    width: 100%;
  }
  .homeglobergt img {
    max-width: 40%;
    margin-top: 30px;
  }
  .quoteimg {
    max-width: 45%;
  }
  .ourstaffdiv {
    padding-bottom: 30px;
  }
  .innerbanner h1 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  #aboutsliderdiv #myCarousel {
    margin-top: 40px;
  }
  .halfdiv {
    width: 100%;
    float: left !important;
  }
  .halflft {
    margin-bottom: 30px;
  }
  .mapwrap img {
    margin-top: 50px;
    margin-bottom: 50px;
  }
  .flexwrap {
    flex-direction: column;
  }
  .whylistlft {
    padding-right: 0;
    width: 100%;
  }
  .toup {
    order: 1 !important;
  }
  .todwn {
    order: 2 !important;
  }
  .whywelist.page-section {
    padding-top: 60px;
    padding-bottom: 60px;
  }
  .whyposterimg {
    margin-top: 30px;
    margin-bottom: 30px;
  }
  img.starlft {
    left: 15px;
  }
  img.starrgt {
    right: 15px;
  }
  .yelplftcol,
  .yelprgtcol {
    width: 100%;
  }
  .yelpwrap {
    margin-bottom: 30px;
  }
  .yelprgtcol {
    margin-left: 0;
  }
  .yelpwrap iframe {
    max-width: 100% !important;
  }
  .yelpcontainer h2 {
    margin-bottom: 30px;
  }
  .moreyelpbtn {
    margin-top: 30px;
  }
  #locgreen {
    padding-top: 45px;
    padding-bottom: 40px;
  }
  .locationwrap {
    padding-bottom: 50px;
  }
  .whyheading {
    margin-top: 35px;
  }
  .openplft {
    margin-bottom: 25px;
  }
  .openplft,
  .openprgt {
    width: 100%;
    float: left;
  }
  .openphours .twothirdcol {
    padding-right: 6px;
  }
  .openphours .onethirdcol {
    padding-left: 6px;
  }
  .starlistspacing li {
    margin-bottom: 10px;
  }
  .openphours .onethirdcol a {
    margin-bottom: 10px;
  }
  #openpbasic .toup {
    margin-bottom: 25px;
  }
  .respitesec {
    text-align: center;
  }
  .respitesec p {
    text-align: left;
  }
  .respitesec .openplft {
    margin-bottom: 30px;
  }
  .respitesec .openplft img {
    margin-bottom: 15px;
  }
  .addinfo {
    margin-top: 25px;
  }
  #pricingsec .twothirdcol {
    padding-right: 0;
  }
  #pricingsec .twothirdcol,
  #pricingsec .onethirdcol {
    width: 100%;
  }
  #pricingsec .onethirdcol {
    max-width: 70%;
    margin-top: 30px;
  }
  #bookparty img {
    margin-top: 55px;
  }
  #pricingsec .startxt {
    margin-bottom: 25px;
  }
  #pricingsec .starlistspacing {
    padding-right: 0;
  }
  .startxt.starlistspacing li::before {
    margin-top: 0px;
  }
  .inlinebtn {
    font-size: 18px;
  }
  .bdayintro p {
    margin-top: 20px;
    margin-bottom: 40px;
  }
  .linemar {
    margin-top: 60px;
    margin-bottom: 60px;
  }
  .bdaytxtadjust .todwn {
    text-align: center;
  }
  .bdaytxtadjust ul {
    margin-top: 15px;
  }
  .specialtripsbtn h4 {
    margin-bottom: 30px;
  }
  .specialtripsbtn img {
    margin-top: 30px !important;
  }
  .flexbox:first-of-type {
    padding-right: 0;
  }
  .flexbox:last-of-type {
    padding-left: 0;
  }
  .packagesthree .pricingbox {
    width: 100%;
  }
  .pricingbox {
    margin-bottom: 35px !important;
  }
  .hbox {
    margin-bottom: 45px;
  }
  .hbox:nth-of-type(2) {
    margin-left: 0;
    margin-right: 0;
  }
  .ourstaffdiv h2 {
    margin-bottom: 40px;
  }
  .threesessions h5 {
    display: block;
  }
  .threesessions span {
    display: none;
  }
  .schoolspricing {
    padding: 35px 15px !important;
    margin-bottom: 0 !important;
  }
  .schoolspricing h3 {
    line-height: 40px;
  }
  .card-header button {
    font-size: 20px;
  }
  .recommimg {
    width: 32%;
  }
  .recommimg:nth-of-type(3n + 2) {
    margin-left: 1%;
    margin-right: 1%;
  }
  .contline {
    margin-top: 20px;
  }
  .blogwrapper {
    display: block;
  }
  .bloglft {
    width: 100%;
  }
  .sidewidget {
    margin-bottom: 30px;
    clear: both;
    margin: 0 auto 30px auto;
    float: none;
    overflow: hidden;
  }
  .reswrap .blogimg,
  .reswrap .bloxexc {
    width: 100%;
  }
  .tywrap img {
    width: 40%;
  }
  .purposeboxes .pricingbox {
    width: 100%;
  }
  .purposelft {
    width: 70%;
  }
  .purpstar {
    max-width: 45px;
  }
  .purposeline {
    margin-bottom: 45px;
  }
  .innerbanner.calbanner h1 {
    font-size: 35px;
  }
  .footer-nav {
    max-width: 100%;
  }
  .footsoc {
    margin-top: 30px;
    margin-bottom: 40px;
  }
  .videotxt {
    height: 215px;
  }
  .videotxt h1 {
    font-size: 34px;
    line-height: 46px;
  }
  .homeglobergt {
    padding-left: 0;
  }
  .blogslider .featslider,
  .bslcont {
    width: 100%;
  }
  .bslcont {
    margin-top: 25px;
  }
  .featslider {
    max-width: 550px;
    margin: 0 auto;
    float: none !important;
  }
  .homeeventbox h5 {
    font-size: 18px;
    line-height: 26px;
  }
  .blogslider .carousel-indicators {
    right: 0;
    bottom: -55px;
  }
  .blogslider {
    padding-bottom: 100px !important;
  }
  .navitems li a {
    padding: 0 0 0 20px !important;
  }
  #navbarResponsive .dropdown-menu li a {
    padding: 0 !important;
  }
  #navbarResponsive .dropdown-menu li:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0 !important;
  }
  #navbarResponsive .dropdown-menu li {
    border-bottom: 0;
    margin-bottom: 0;
  }
  #navbarResponsive .dropdown-menu {
    margin-top: 10px;
    padding: 15px 20px;
  }
  .footinst {
    margin-bottom: 30px;
  }
  .healwrap {
    margin-bottom: 10px;
  }
  .openphours .onethirdcol a {
    padding: 14px 0;
  }
  .schoolsline {
    margin-bottom: 35px;
  }
  #contactform input {
    height: 55px;
  }
  .singblogline {
    display: block;
  }
}

@media (max-width: 767px) {
  header {
    position: relative;
    width: 100%;
  }
  .res {
    background: red;
  }
  h2 {
    font-size: 26px;
    line-height: 36px;
    margin-bottom: 15px;
  }
  h3 {
    font-size: 24px;
    line-height: 30px;
  }
  h4,
  h5 {
    font-size: 15px;
    line-height: 20px;
  }
  p,
  p.biggertxt,
  .startxt li,
  .startxt.normalfontlist li,
  .startxt.smallfontlist li,
  #pricingsec .startxt li,
  .packagesthree ul li,
  .card-body,
  .bloxexc p,
  section li,
  .card-body ul li {
    font-size: 15px;
    line-height: 20px;
  }
  p {
    margin-bottom: 15px;
  }
  .headerhigh {
    height: 0px;
  }
  .navbar-brand {
    margin: 0 auto 15px !important;
    float: none;
    width: 100%;
    text-align: center;
  }
  .navitems li a {
    padding: 0px !important;
  }
  .navitems ul ul {
    padding-left: 0 !important;
  }
  .headertop {
    text-align: center;
    width: 100%;
    margin-right: 0 !important;
  }
  .wrtsbtn,
  header .wrtsbtn {
    padding: 7px 17px;
  }
  .headersocial a {
    width: 35px;
    height: 35px;
  }
  .headersocial a i {
    font-size: 22px;
    line-height: 37px;
  }
  .hbox {
    padding-bottom: 65px;
    max-width: 100%;
  }
  .hbox:nth-of-type(2) {
    margin-left: 15px;
    margin-right: 15px;
  }
  .hbox:last-of-type {
    margin-bottom: 0;
  }
  .hbox .yellowbtn {
    width: 140px;
    margin-left: -70px;
  }
  .carousel .testimonial {
    font-size: 22px;
  }
  .carousel .overview {
    font-size: 18px;
  }
  .carousel .item {
    min-height: 200px;
  }
  .staffmember {
    width: 43%;
  }
  .ourstaffdiv {
    padding-bottom: 0;
  }
  .staffmember span {
    display: inline;
  }
  .startxt {
    padding-left: 25px;
  }
  .startxt li::before {
    width: 15px;
    height: 14px;
  }
  .carousel-indicators li {
    width: 7px;
    height: 7px;
    margin: 1px 2px;
    border: 2px solid #ffffff !important;
  }
  #tabs li {
    font-size: 15px;
    line-height: 20px;
  }
  .locationwrap {
    width: 50%;
  }
  .locationwrap {
    margin-bottom: 55px;
    padding-bottom: 35px;
  }
  .tabsline {
    margin-top: 20px;
    margin-bottom: 40px;
  }
  .btnlineloc .wrtsbtn {
    margin-top: 40px;
  }
  .nonprofline {
    margin-top: 25px;
    margin-bottom: 40px;
  }
  .page-section {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .twothirdcol,
  .onethirdcol {
    width: 100%;
  }
  .openphours .onethirdcol h6 {
    margin-bottom: 20px;
  }
  .openphours .twothirdcol {
    padding-right: 0;
    margin-bottom: 30px;
  }
  .openphours .onethirdcol {
    padding-left: 0;
  }
  .greenlft img {
    width: 60%;
  }
  #pricingsec .onethirdcol {
    max-width: 100%;
  }
  #pricingsec .onethirdcol {
    margin-top: 0;
  }
  #bookparty h2,
  #bookparty .bookawrap {
    width: 100%;
  }
  #bookparty h2 {
    margin-bottom: 15px;
  }
  .pricingbox {
    width: 100%;
    margin-bottom: 25px;
    padding-bottom: 25px;
  }
  .pricingbox:first-of-type,
  .pricingbox:nth-of-type(2) {
    margin-bottom: 25px;
  }
  .pricingbox:last-of-type {
    margin-bottom: 0;
  }
  .linemar {
    margin-top: 40px;
    margin-bottom: 40px;
  }
  .bdaytxtadjust h5.yellowtxt {
    margin-top: 15px;
  }
  .bdaytxtadjust img {
    margin-top: 10px;
  }
  .inlinebtn {
    font-size: 13px;
  }
  .wheelsline {
    margin-top: 30px;
  }
  .staffttl {
    margin-bottom: 30px;
  }
  .schoolsline {
    margin-top: 30px;
  }
  .card-header button {
    font-size: 15px;
  }
  .recommimg {
    width: 48%;
    padding-top: 8%;
    padding-bottom: 8%;
  }
  .recommimg:nth-of-type(3n + 2) {
    margin-left: inherit;
    margin-right: inherit;
  }
  .sidewidget {
    padding: 30px;
  }
  .blogsidebar h3 {
    margin-bottom: 20px;
  }
  .blogimg {
    width: 100%;
    text-align: center;
  }
  .bloxexc {
    width: 100%;
    float: left;
    margin-top: 25px;
  }
  .bloxexc h2 {
    font-size: 20px;
    line-height: 30px;
  }
  .bloxexc h5 {
    font-size: 13px;
  }
  .sidebarimg a.inlinebtn {
    margin-top: 30px;
    margin-bottom: 0;
  }
  .purposelft {
    width: 100%;
  }
  .purposeboxes .pricingbox {
    text-align: center;
    padding: 30px !important;
  }
  .purpkid {
    float: none;
    margin-top: 10px !important;
    max-width: 100px;
  }
  .purposeline {
    margin-top: 0;
    margin-bottom: 25px;
  }
  .innerbanner.calbanner h1 {
    font-size: 25px;
    line-height: 35px;
  }
  .ipadhide {
    display: none;
  }
  .footaddress .yellowtxt,
  .footinst .yellowtxt {
    display: block;
  }
  .footer-nav a {
    font: 14px "gothambook";
    padding-left: 1px;
    padding-right: 1px;
    display: block;
    padding: 10px;
  }
  .footer-nav {
    max-width: 80%;
  }
  .videotxt {
    height: 215px;
  }
  #abouthome .wrtsbtn {
    margin-top: 20px;
  }
  #abouthome {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .homeeventbox {
    width: 100%;
  }
  .homeeventbox:nth-of-type(2) {
    margin: 30px 0;
  }
  .blogslider .carousel-indicators {
    bottom: -40px;
  }
  .staffmember:last-of-type {
    margin-bottom: 0;
  }
  .blogslider {
    padding-bottom: 80px !important;
  }
  #homeeventswrap h2 {
    margin-bottom: 30px;
  }
  .oopstxt h2 {
    font-size: 65px;
    margin-top: 40px;
  }
  .oopstxt p {
    font-size: 44px;
    line-height: 59px;
  }
  .oopsline {
    margin-top: -30px;
  }
  #oopseventswrap {
    padding-top: 15px;
  }
  #oopseventswrap .container {
    margin-top: 25px;
  }
  #oopseventswrap .homeeventbox:nth-of-type(2) {
    margin-left: 0;
  }
  .footinst {
    margin-bottom: 20px;
  }
  #accordionfaq {
    margin-top: 10px;
  }
  .schoolsline {
    margin-bottom: 20px;
  }
  #contactform input {
    width: 100%;
    height: 45px;
    margin-bottom: 10px;
    padding-left: 20px;
  }
  #contactform textarea {
    padding-left: 20px;
    padding-top: 20px;
    min-height: 140px;
  }
  #contactform input#submit {
    margin-top: 30px;
  }
  #contformsec {
    padding-top: 35px;
    padding-bottom: 35px;
  }
  #contformsec h2 {
    margin-bottom: 35px;
  }
  .headersocial a {
    font-size: 20px;
  }
  .headersocial a::before {
    line-height: 38px;
  }
  .contapp h6 {
    font-size: 20px;
  }
  .staffmember {
    margin-bottom: 30px;
  }
  .staffmember:last-of-type {
    margin-bottom: 0;
  }
}

@media (max-width: 575px) {
  .res {
    background: green;
  }
  .headerhigh {
    height: 0px;
  }
  h1 {
    font-size: 34px;
    line-height: 44px;
  }
  h2 {
    font-size: 20px;
    line-height: 30px;
  }
  h3 {
    font-size: 20px;
    line-height: 26px;
  }
  .whiteborderimg,
  .wrtscalendarwrap img {
    border: 3px solid #ffffff;
  }
  .flexwrap {
    flex-direction: column;
  }
  .page-section {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  #abouthome .wrtsbtn {
    margin-left: 6px;
    margin-right: 6px;
    margin-top: 15px;
  }
  #abouthome img.line {
    margin-top: 30px;
    margin-bottom: 30px;
  }
  .carousel .testimonial {
    font-size: 18px;
  }
  .carousel .overview {
    font-size: 14px;
  }
  .carousel .item {
    min-height: 170px;
  }
  .staffmember {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .ourstaffdiv h2 {
    margin-bottom: 35px;
  }
  .innerbanner h1 {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  #aboutsliderdiv #myCarousel {
    margin-top: 20px;
  }
  .yellowfull h2 {
    padding-top: 35px;
    padding-bottom: 35px;
  }
  .whywelist.page-section {
    padding-top: 35px;
    padding-bottom: 35px;
  }
  #whyposter h4 {
    margin-bottom: 30px;
  }
  #locgreen {
    padding-top: 25px;
    padding-bottom: 20px;
  }
  #tabs {
    flex-direction: column;
    max-width: 300px;
    margin: 0 auto;
  }
  #tabs li {
    margin: 0 0 8px;
    font-size: 13px;
    line-height: 16px;
    padding: 12px 10px;
  }
  .locationwrap {
    width: 100%;
  }
  .locationwrap h3 {
    margin-top: 20px;
    margin-bottom: 15px;
  }
  .locationwrap {
    padding-bottom: 30px;
  }
  .btnlineloc {
    margin-top: -35px;
  }
  .btnlineloc .wrtsbtn {
    margin-top: 30px;
  }
  .greenlft,
  .greenrgt {
    width: 100%;
    margin: 0;
    text-align: center;
  }
  .greenlft {
    margin-bottom: 25px;
  }
  .greenlft img {
    width: 20%;
  }
  #bookparty img {
    margin-top: 30px;
  }
  .inlinebtn {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  .pricingbox img {
    margin-top: 10px;
    margin-bottom: 25px;
  }
  .bdayintro p {
    margin-bottom: 20px;
  }
  .packagesthree h5 {
    margin-bottom: 15px;
  }
  .bdaytxtadjust h5 {
    line-height: 24px;
  }
  .schoolspricing {
    padding: 20px 15px !important;
  }
  .card-body ul,
  .card-body ol {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .recommimg {
    width: 100%;
    margin-bottom: 30px;
  }
  .contline {
    margin-bottom: 25px;
  }
  .contapp img {
    display: block;
    margin: 0 auto 10px auto;
  }
  .contapp img:last-of-type {
    margin: 0;
    display: inline-block;
  }
  .tywrap img {
    width: 55%;
  }
  .purposeposter a {
    margin-top: 30px;
  }
  .wrtscalendarwrap img {
    margin-bottom: 15px;
  }
  .innerbanner.calbanner h1 {
    font-size: 20px;
    line-height: 26px;
  }
  .footer-nav {
    max-width: 100%;
  }
  .footsoc {
    margin-top: 20px;
    margin-bottom: 25px;
  }
  .footerfr {
    max-width: 85px;
  }
  .videotxt {
    height: 145px;
  }
  .videotxt h1 {
    font-size: 20px;
    line-height: 28px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .abouthomeapp span.greentxt {
    display: block;
  }
  .blogslider h3 {
    font-size: 17px;
  }
  .blogslider p {
    font-size: 14px;
  }
  .dateslider {
    width: 75px;
    height: 75px;
  }
  .bslday {
    font-size: 30px;
    margin-top: 2px;
  }
  .bslmonth {
    font-size: 15px;
  }
  #myCarousel8.carousel {
    margin-top: 20px;
  }
  .homeeventbox h5 {
    font-size: 16px;
    line-height: 24px;
  }
  .oopstxt,
  .oopsimg {
    width: 100%;
    text-align: center;
  }
  .oopsimg {
    margin-top: 30px;
  }
  .oopstxt h2 {
    font-size: 45px;
    margin-top: 15px;
  }
  .oopstxt p {
    font-size: 28px;
    line-height: 34px;
  }
  .oopsimg img {
    max-width: 100px;
  }
  .openphours .onethirdcol a {
    font-size: 13px;
    padding: 10px 0;
  }
  .headersocial a {
    margin-right: 0;
  }
  .hourssep {
    display: none;
  }
  .headertop .yellowtxt {
    display: block;
  }
}

@media (max-width: 480px) {
  .res {
    background: orange;
  }
  .headerhigh {
    height: 0px;
  }
  h1 {
    font-size: 26px;
    line-height: 36px;
  }
  .headersocial a {
    margin-left: 0px;
  }
  .wrtsbtn,
  header .wrtsbtn {
    font-size: 13px;
  }
  .abouthomeapp img {
    margin-bottom: 15px;
  }
  .homeglobergt img {
    max-width: 70%;
  }
  .carousel .testimonial {
    font-size: 15px;
  }
  .innerbanner h1 {
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .mapwrap {
    padding-top: 45px;
    padding-bottom: 45px;
  }
  .mapwrap img {
    margin-top: 15px;
    margin-bottom: 25px;
  }
  .locnumbers span {
    display: block;
  }
  .locnumbers span.commaloc {
    display: none;
  }
  .whyheading {
    margin-top: 25px;
  }
  .nonprofline {
    margin-top: 15px;
    margin-bottom: 25px;
  }
  .greenlft img {
    width: 30%;
  }
  #bookparty a {
    font-size: 20px;
  }
  .inlinebtn {
    display: block;
  }
  .ourstaffdiv h2 {
    margin-bottom: 25px;
  }
  .contmail {
    font-size: 12px;
  }
  .tywrap img {
    width: 70%;
  }
  .videotxt {
    height: 130px;
  }
  .videotxt h3 {
    font-size: 14px;
    line-height: 20px;
  }
  #homeeventswrap h2 a {
    display: block;
  }
  .dropdown-menu {
    min-width: 100%;
    max-width: 100%;
  }
  .contapp h6 {
    font-size: 17px;
  }
}
