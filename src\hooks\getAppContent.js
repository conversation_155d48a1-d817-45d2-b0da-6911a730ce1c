import { useStaticQuery, graphql } from 'gatsby';

export const useAppContent = () => {
    const data = useStaticQuery(graphql`
        query GET_APP_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                title
                content
            }
        }
    `);

    if (!data?.wpPage) {
        console.error('App content not found');
        return null;
    }

    // Temporary fallback values until ACF is configured
    const appTitle = 'We Rock the Spectrum App';
    const appContent = 'ACF fields are being configured. App content will be restored once WordPress ACF integration is complete.';
    const appLearnMoreLink = '#';
    const appNonProfitLink = '#';
    const appDownloadText = 'Download App';
    const appRightSideImage = null;

    return {
        appTitle,
        appContent,
        appLearnMoreLink,
        appNonProfitLink,
        appDownloadText,
        appRightSideImage: null,
    };
};
