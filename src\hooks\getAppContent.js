import { useStaticQuery, graphql } from 'gatsby';

export const useAppContent = () => {
    const data = useStaticQuery(graphql`
        query GET_APP_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    appTitle
                    appContent
                    appLearnMoreLink
                    appNonProfitLink
                    appDownloadText
                    appRightSideImage {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('App content not found');
        return null;
    }

    const {
        appTitle,
        appContent,
        appLearnMoreLink,
        appNonProfitLink,
        appDownloadText,
        appRightSideImage,
    } = data.wpPage.acf;

    return {
        appTitle,
        appContent,
        appLearnMoreLink,
        appNonProfitLink,
        appDownloadText,
        appRightSideImage: appRightSideImage?.localFile?.childImageSharp
            ? appRightSideImage.localFile.childImageSharp.gatsbyImageData
            : null,
    };
};
