import { useStaticQuery, graphql } from 'gatsby';

export const useAppContent = () => {
    const data = useStaticQuery(graphql`
        query GET_APP_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    app_title
                    app_content
                    app_learn_more_link
                    app_non_profit_link
                    app_download_text
                    app_right_side_image {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 400)
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('App content not found');
        return null;
    }

    const {
        app_title,
        app_content,
        app_learn_more_link,
        app_non_profit_link,
        app_download_text,
        app_right_side_image,
    } = data.wpPage.acf;

    return {
        appTitle: app_title,
        appContent: app_content,
        appLearnMoreLink: app_learn_more_link,
        appNonProfitLink: app_non_profit_link,
        appDownloadText: app_download_text,
        appRightSideImage: app_right_side_image?.localFile?.childImageSharp
            ? app_right_side_image.localFile.childImageSharp.gatsbyImageData
            : null,
    };
};
