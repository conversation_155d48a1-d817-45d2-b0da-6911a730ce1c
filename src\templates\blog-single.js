import React from 'react';
import '../styles/app.scss';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import SidebarCategories from '../components/sidebar/Categories';
import Search from '../components/sidebar/Search';
import Archives from '../components/sidebar/Archives';
import Schedule from '../components/sidebar/Schedule';
import Shop from '../components/sidebar/Shop';
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';

const Blog = ({ data }) => {
    const { wpPost: post } = data;

    const seoTitle = post.seo?.title?.replace('&#039;', "'") || post.title;
    const seoDescription = post.seo?.metaDesc || post.excerpt;

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <section className="innerbanner">
                <div className="container">
                    <h1>Read Our Blog</h1>
                </div>
            </section>

            <section className="page-section">
                <div className="container blogwrapper blogarticle">
                    <div className="bloglft">
                        <div className="blogimg">
                            {post.featuredImage?.node?.localFile && (
                                <img
                                    src={
                                        post.featuredImage.node.localFile
                                            ?.childImageSharp?.fluid?.src || ''
                                    }
                                    alt={post.title || 'Featured Image'}
                                />
                            )}
                        </div>
                        <div className="bloxexc">
                            <h2
                                dangerouslySetInnerHTML={{
                                    __html: post.title,
                                }}
                            />
                            <h5>
                                {new Intl.DateTimeFormat('en-US', {
                                    month: 'long',
                                    day: 'numeric',
                                    year: 'numeric',
                                }).format(new Date(post.date))}
                            </h5>
                        </div>
                        <img
                            className="blogline"
                            src={LineFull}
                            alt="Decorative line"
                        />
                        <div
                            dangerouslySetInnerHTML={{
                                __html: post.content,
                            }}
                        />
                    </div>
                    <div className="blogsidebar">
                        <SidebarCategories />
                        <Search />
                        <Archives />
                        <Schedule />
                        <Shop />
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default Blog;

export const pageQuery = graphql`
    query($id: String!) {
        wpPost(id: { eq: $id }) {
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            fluid {
                                src
                            }
                        }
                    }
                }
            }
            categories {
                name
            }
            date
            excerpt
            slug
            content
            title
            seo {
                title
                metaDesc
            }
        }
    }
`;