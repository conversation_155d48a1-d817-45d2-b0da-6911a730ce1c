import { useStaticQuery, graphql } from 'gatsby';

export const useHomeSeo = () => {
    const data = useStaticQuery(graphql`
        query GET_HOME_SEO {
            wpPage(slug: { eq: "homepage" }) {
                seo {
                    title
                    metaDesc
                    opengraphImage {
                        sourceUrl
                    }
                    opengraphUrl
                    opengraphTitle
                    opengraphDescription
                }
            }
        }
    `);

    if (!data?.wpPage?.seo) {
        console.error('SEO data for the homepage not found');
        return {
            yoastTitle: '',
            yoastMeta: '',
        };
    }

    const { title, metaDesc } = data.wpPage.seo;

    return {
        yoastTitle: title || '',
        yoastMeta: metaDesc || '',
    };
};
