import { useStaticQuery, graphql } from 'gatsby';

export const useMenuitems = () => {
    const data = useStaticQuery(
        graphql`
            query GET_ALL_MENU_ITEMS {
                allWpMenuItem {
                    nodes {
                        id
                        title
                        url
                        locations
                        childItems {
                            nodes {
                                id
                                title
                                url
                            }
                        }
                    }
                }
            }
        `
    );

    if (!data?.allWpMenuItem?.nodes) {
        console.error('No menu items found');
        return [];
    }

    return data.allWpMenuItem.nodes
        .filter(menuItem => menuItem.locations?.includes('PRIMARY')) 
        .map(menuItem => ({
            id: menuItem.id,
            title: menuItem.title,
            url: menuItem.url,
            children: menuItem.childItems?.nodes.map(child => ({
                id: child.id,
                title: child.title,
                url: child.url,
            })) || [],
        }));
};
