import { useStaticQuery, graphql } from 'gatsby';

export const useFooterMenuitems = () => {
    const data = useStaticQuery(graphql`
        query GET_FOOTER_MENU_ITEMS {
            allWpMenuItem {
                nodes {
                    id
                    title
                    url
                }
            }
        }
    `);

    if (!data?.allWpMenuItem?.nodes) {
        console.error('No footer menu items found');
        return [];
    }

    return data.allWpMenuItem.nodes;
};
