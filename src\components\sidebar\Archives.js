/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */
/* eslint-disable prefer-const */
import React from 'react';
import { useArchiveData } from '../../hooks/getArchiveData';

const ArchivesList = () => {
    const blogPostsData = useArchiveData();
    const blogPosts = blogPostsData.allWpPost.nodes;

    const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    const monthArchive = {};

    // Organize posts by year and month
    blogPosts.forEach(post => {
        const postDate = new Date(post.date);
        const postYear = postDate.getFullYear();
        const postMonth = postDate.getMonth();

        if (!monthArchive[postYear]) {
            monthArchive[postYear] = {};
        }
        if (!monthArchive[postYear][postMonth]) {
            monthArchive[postYear][postMonth] = [];
        }

        monthArchive[postYear][postMonth].push(post);
    });

    // Render the archive list
    return (
        <div>
            {Object.keys(monthArchive)
                .sort((a, b) => b - a) // Sort years in descending order
                .map(year => (
                    <div key={year}>
                        {Object.keys(monthArchive[year])
                            .sort((a, b) => b - a) // Sort months in descending order
                            .map(month => {
                                const posts = monthArchive[year][month];
                                return (
                                    <h5 key={`${year}-${month}`}>
                                        <a href={`/archive/${monthNames[month]}-${year}`}>
                                            {monthNames[month]} ({year}){' '}
                                            <span>{posts.length}</span>
                                        </a>
                                    </h5>
                                );
                            })}
                    </div>
                ))}
        </div>
    );
};

const Archives = () => (
    <div className="sidewidget">
        <h3>ARCHIVES</h3>
        <ArchivesList />
    </div>
);

export default Archives;
