import { useStaticQuery, graphql } from 'gatsby';

export const useFooterContent = () => {
    const data = useStaticQuery(graphql`
        query GET_FOOTER_CONTENT {
            wpFooterContent {
                aCFFooter {
                    footerAddress
                    footerPhoneNumber
                }
            }
        }
    `);

    if (!data?.wpFooterContent?.aCFFooter) {
        console.error('Footer content not found');
        return {
            footerAddress: 'Address not available',
            footerPhoneNumber: 'Phone number not available',
        };
    }

    return data.wpFooterContent.aCFFooter;
};
