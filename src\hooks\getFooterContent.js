import { useStaticQuery, graphql } from 'gatsby';

export const useFooterContent = () => {
    const data = useStaticQuery(graphql`
        query GET_FOOTER_CONTENT {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    footerAddress
                    footerPhoneNumber
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Footer content not found');
        return {
            footerAddress: 'Address not available',
            footerPhoneNumber: 'Phone number not available',
        };
    }

    return data.wpPage.acf;
};
