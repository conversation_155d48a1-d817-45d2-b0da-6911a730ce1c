import { useStaticQuery, graphql } from 'gatsby';

export const useFooterContent = () => {
    const data = useStaticQuery(graphql`
        query GET_FOOTER_CONTENT {
            wp {
                acf {
                    footerContent {
                        footer_address
                        footer_phone_number
                    }
                }
            }
        }
    `);

    if (!data?.wp?.footerContent) {
        console.error('Footer content not found');
        return {
            footer_address: 'Address not available',
            footer_phone_number: 'Phone number not available',
        };
    }

    return data.wp.footerContent;
};
