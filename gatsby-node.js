/* eslint-disable prefer-const */
const _ = require('lodash');
const path = require('path');

// const { createFilePath } = require('gatsby-source-filesystem');
// const { paginate } = require('gatsby-awesome-pagination');

const getOnlyPublished = edges =>
    _.filter(edges, ({ node }) => node.status === 'publish');

const monthArchive = {
    2030: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2029: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2028: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2027: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2026: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2025: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2024: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	2023: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2022: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	2021: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
	
    2020: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },

    2019: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2018: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2017: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2016: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2015: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2014: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2013: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2012: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
    2011: {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
        8: [],
        9: [],
        10: [],
        11: [],
    },
};

const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
];
exports.createPages = async ({ graphql, actions }) => {
    const { createPage } = actions;

    // Query for Blog Posts
    const blogResult = await graphql(`
        {
            allWpPost {
                nodes {
                    id
                    slug
                }
            }
        }
    `);

    if (blogResult.errors) {
        throw new Error("Error fetching blog posts: ", blogResult.errors);
    }

    blogResult.data.allWpPost.nodes.forEach(post => {
        createPage({
            path: `/blog/${post.slug}/`,
            component: path.resolve(`./src/templates/blog-single.js`),
            context: {
                id: post.id,
            },
        });
    });

    // Query for Events
    const eventsResult = await graphql(`
        {
            allWpEventsPostType {
                nodes {
                    id
                    slug
                }
            }
        }
    `);

    if (eventsResult.errors) {
        throw new Error("Error fetching events: ", eventsResult.errors);
    }

    eventsResult.data.allWpEventsPostType.nodes.forEach(event => {
        createPage({
            path: `/events/${event.slug}/`,
            component: path.resolve(`./src/templates/single-event.js`),
            context: {
                id: event.id,
            },
        });
    });

    // Query for Resources
    const resourcesResult = await graphql(`
        {
            allWpResourcesPostType {
                nodes {
                    id
                    slug
                }
            }
        }
    `);

    if (resourcesResult.errors) {
        throw new Error("Error fetching resources: ", resourcesResult.errors);
    }

    resourcesResult.data.allWpResourcesPostType.nodes.forEach(resource => {
        createPage({
            path: `/resources/${resource.slug}/`,
            component: path.resolve(`./src/templates/singleResource.js`),
            context: {
                id: resource.id,
            },
        });
    });
};
