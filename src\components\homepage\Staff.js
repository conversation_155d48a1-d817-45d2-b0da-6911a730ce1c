import React from 'react';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import { useStaffMembers } from '../../hooks/getStaffMembers';

const Staff = bgType => {
    const data = useStaffMembers();
    const StaffContent = data.allWpAcfOptions.nodes[0].options; // Updated schema
    const StaffTitle = StaffContent.our_staff_section_title;
    const StaffList = StaffContent.our_staff_list_members;

    let bgClass = bgType.bgType;
    if (bgType.bgType === 'secondary') {
        bgClass = 'bg-secondary';
    } else {
        bgClass = 'bg-primary';
    }

    return (
        <section className={`page-section ${bgClass} text-white centersec staffsec display_none`}>
            <div className="container">
                <h2
                    className="staffttl"
                    dangerouslySetInnerHTML={{
                        __html: StaffTitle,
                    }}
                />
                {StaffList.map(staffMember => (
                    <div
                        className="staffmember"
                        key={staffMember.staff_member_fullname}
                    >
                        <GatsbyImage
                            className="whiteborderimg"
                            image={getImage(
                                staffMember.staff_member_image.localFile
                            )}
                            alt={staffMember.staff_member_fullname || 'Staff Member'}
                        />
                        <h3
                            dangerouslySetInnerHTML={{
                                __html: staffMember.staff_member_fullname,
                            }}
                        />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: staffMember.staff_member_position,
                            }}
                        />
                    </div>
                ))}
            </div>
        </section>
    );
};

export default Staff;
