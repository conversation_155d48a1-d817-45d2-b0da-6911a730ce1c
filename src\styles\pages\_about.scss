.aboutus_slider {
	.slick-dots {
		position: absolute;
		bottom: 15px;
		display: block;
		width: auto;
		right: 15px;
		li {
			&.slick-active {
				button {
					&:before {
						background-color: white;
						opacity: 1;
					}
				}
			}
			button {
				&:before {
					width: 17px;
					height: 17px;
					opacity: 1;
					border: 3px solid white;
					border-radius: 50%;
					content: " ";
				}
			}
		}
	}
}

.slider_owners {
	.slick-dots {
		position: absolute;
		bottom: 15px;
		li {
			&.slick-active {
				button {
					&:before {
						background-color: white;
						opacity: 1;
					}
				}
			}
			button {
				&:before {
					width: 17px;
					height: 17px;
					opacity: 1;
					border: 3px solid white;
					border-radius: 50%;
					content: " ";
				}
			}
		}
	}
}
