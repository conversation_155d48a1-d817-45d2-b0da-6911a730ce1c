import { useStaticQuery, graphql } from 'gatsby';

export const useResourcesTeam = () => {
    const data = useStaticQuery(graphql`
        query GET_RESOURCE_TEAM {
            allWpResourcesPostType {
                nodes {
                    id
                    title
                    slug
                    excerpt
                    featuredImage {
                        node {
                            localFile {
                                childImageSharp {
                                    gatsbyImageData(layout: CONSTRAINED, width: 400)
                                }
                            }
                            altText
                        }
                    }
                }
            }
        }
    `);

    if (!data?.allWpResourcesPostType?.nodes) {
        console.error('No resource team data found');
        return [];
    }

    return data.allWpResourcesPostType.nodes.map(resource => ({
        id: resource.id,
        title: resource.title,
        slug: resource.slug,
        excerpt: resource.excerpt,
        featuredImage: resource.featuredImage?.node?.localFile?.childImageSharp
            ? {
                  image: resource.featuredImage.node.localFile.childImageSharp.gatsbyImageData,
                  altText: resource.featuredImage.node.altText || 'Resource image',
              }
            : null,
    }));
};
