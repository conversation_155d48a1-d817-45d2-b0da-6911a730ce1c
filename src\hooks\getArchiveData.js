import { useStaticQuery, graphql } from 'gatsby';

export const useArchiveData = () => {
    const data = useStaticQuery(graphql`
        query GET_ARCHIVE_DATA {
            allWpPost(sort: {date: DESC}) {
                nodes {
                    id
                    date
                    title
                    uri
                }
            }
        }
    `);

    if (!data?.allWpPost?.nodes) {
        console.error('No archive data found');
        return [];
    }

    return data.allWpPost.nodes.map(post => ({
        id: post.id,
        date: post.date,
        title: post.title,
        uri: post.uri,
    }));
};
