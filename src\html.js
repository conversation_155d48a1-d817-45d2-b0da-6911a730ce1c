import React from 'react';
import PropTypes from 'prop-types';

export default function HTML(props) {
    return (
        <html {...props.htmlAttributes}>
            <head>
                <meta charSet="utf-8" />
                <meta httpEquiv="x-ua-compatible" content="ie=edge" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1, shrink-to-fit=no"
                />
                <script
                    src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"
                    defer
                ></script>
                <script
                    src="https://widgets.mindbodyonline.com/javascripts/healcode.js"
                    defer
                ></script>
                {props.headComponents}
                <script
                    async
                    src="https://www.googletagmanager.com/gtag/js?id=G-0T05XNN4TX"
                ></script>
                <script
                    dangerouslySetInnerHTML={{
                        __html: `
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', 'G-0T05XNN4TX');
                        `,
                    }}
                />
            </head>
            <body {...props.bodyAttributes}>
                {props.preBodyComponents}
                <div
                    key="body"
                    id="___gatsby"
                    dangerouslySetInnerHTML={{ __html: props.body }}
                />
                {props.postBodyComponents}
                <script
                    dangerouslySetInnerHTML={{
                        __html: `
                            var element = document.getElementById("mainNav");
                            if (element) {
                                window.addEventListener("scroll", function () {
                                    if (document.documentElement.scrollTop > 400) {
                                        element.classList.add("fixedd");
                                    } else {
                                        element.classList.remove("fixedd");
                                    }
                                }, false);
                            }
                            $("body").on("click", "[data-popup]", function () {
                                var xx = $(this).data("popup");
                                $("." + xx).addClass("show");
                            });
                            $("body").on("click", ".close_popup", function () {
                                $(".popup-wrap").removeClass("show");
                            });
                        `,
                    }}
                />
            </body>
        </html>
    );
}

HTML.propTypes = {
    htmlAttributes: PropTypes.object,
    headComponents: PropTypes.array,
    bodyAttributes: PropTypes.object,
    preBodyComponents: PropTypes.array,
    body: PropTypes.string,
    postBodyComponents: PropTypes.array,
};
