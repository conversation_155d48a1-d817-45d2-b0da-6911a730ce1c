import { useStaticQuery, graphql } from 'gatsby';

export const useHeaderInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_HEADER_INFO {
            wp {
                headerInfo {
                    enable_header_banner
                    header_banner_message_content
                }
            }
        }
    `);

    if (!data?.wp?.headerInfo) {
        console.error('Header information not found');
        return {
            enable_header_banner: false,
            header_banner_message_content: '',
        };
    }

    return data.wp.headerInfo;
};
