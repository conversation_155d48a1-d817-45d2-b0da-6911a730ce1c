import { useStaticQuery, graphql } from 'gatsby';

export const useHeaderInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_HEADER_INFO {
            wpPage(slug: { eq: "homepage" }) {
                acf {
                    enableHeaderBanner
                    headerBannerMessageContent
                    logoHeader {
                        localFile {
                            childImageSharp {
                                gatsbyImageData(layout: CONSTRAINED, width: 200)
                            }
                        }
                    }
                    headerWorkingHours
                    headerContactNumber
                    headerRegisterButtonText
                }
            }
        }
    `);

    if (!data?.wpPage?.acf) {
        console.error('Header information not found');
        return {
            enableHeaderBanner: false,
            headerBannerMessageContent: '',
            logoHeader: null,
            headerWorkingHours: '',
            headerContactNumber: '',
            headerRegisterButtonText: '',
        };
    }

    return data.wpPage.acf;
};
