import { useStaticQuery, graphql } from 'gatsby';

export const useHeaderInfo = () => {
    const data = useStaticQuery(graphql`
        query GET_HEADER_INFO {
            wpPage(slug: { eq: "homepage" }) {
                title
                content
            }
        }
    `);

    if (!data?.wpPage) {
        console.error('Header information not found');
        return {
            enableHeaderBanner: false,
            headerBannerMessageContent: '',
            logoHeader: null,
            headerWorkingHours: '',
            headerContactNumber: '',
            headerRegisterButtonText: '',
        };
    }

    // Temporary fallback values until ACF is configured
    return {
        enableHeaderBanner: false,
        headerBannerMessageContent: '',
        logoHeader: null,
        headerWorkingHours: 'Mon-Fri 9AM-6PM',
        headerContactNumber: '(555) 123-4567',
        headerRegisterButtonText: 'Register Now',
    };
};
