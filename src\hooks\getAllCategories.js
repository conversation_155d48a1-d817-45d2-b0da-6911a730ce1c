import { useStaticQuery, graphql } from 'gatsby';

export const useAllCategories = () => {
    const data = useStaticQuery(graphql`
        query GET_ALL_CATEGORIES {
            allWpCategory {
                nodes {
                    name
                    slug
                }
            }
        }
    `);

    if (!data?.allWpCategory?.nodes) {
        console.error('No categories found');
        return [];
    }

    return data.allWpCategory.nodes;
};
