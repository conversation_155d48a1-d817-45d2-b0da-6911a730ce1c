import { useStaticQuery, graphql } from 'gatsby';

export const useSocialLinks = () => {
    const data = useStaticQuery(graphql`
        query GET_SOCIAL_LINKS {
            wp {
                socialLinks {
                    social_facebook_link
                    social_instagram_link
                }
            }
        }
    `);

    if (!data?.wp?.socialLinks) {
        console.error('Social links data not found');
        return {
            social_facebook_link: '',
            social_instagram_link: '',
        };
    }

    return data.wp.socialLinks;
};
