import { useStaticQuery, graphql } from 'gatsby';

export const useSocialLinks = () => {
    const data = useStaticQuery(graphql`
        query GET_SOCIAL_LINKS {
            wp {
                generalSettings {
                    title
                }
            }
        }
    `);

    if (!data?.wp) {
        console.error('Social links data not found');
        return [];
    }

    // Temporary fallback values until <PERSON><PERSON> is configured
    return [
        {
            socialNetworkName: 'Facebook',
            socialNetworkUrl: '#',
            socialNetworkIcon: null,
        },
        {
            socialNetworkName: 'Instagram',
            socialNetworkUrl: '#',
            socialNetworkIcon: null,
        }
    ];
};
