import { useStaticQuery, graphql } from 'gatsby';

export const useSocialLinks = () => {
    const data = useStaticQuery(graphql`
        query GET_SOCIAL_LINKS {
            wp {
                socialNetworks {
                    aCFOptionSocialNetworks {
                        socialNetworksList {
                            socialNetworkName
                            socialNetworkUrl
                            socialNetworkIcon {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(layout: CONSTRAINED, width: 50)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    `);

    if (!data?.wp?.socialNetworks?.aCFOptionSocialNetworks?.socialNetworksList) {
        console.error('Social links data not found');
        return [];
    }

    return data.wp.socialNetworks.aCFOptionSocialNetworks.socialNetworksList.map(social => ({
        socialNetworkName: social.socialNetworkName,
        socialNetworkUrl: social.socialNetworkUrl,
        socialNetworkIcon: social.socialNetworkIcon?.localFile?.childImageSharp?.gatsbyImageData,
    }));
};
