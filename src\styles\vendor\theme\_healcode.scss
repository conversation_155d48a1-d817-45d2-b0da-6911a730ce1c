.mfp-content {
	background: #fff;
}
#schedule {
	padding: 30px;
}
#register {
	padding: 30px;
}

.healcode-btn {
	display: inline-block;
}
.healcode-btn-red .squareButton.global > a {
	background-color: #ee1d3a;
	border-color: #ee1d3a;
}

.btn-col {
	text-align: center;
}
.btn-col .healcode-btn {
	margin-bottom: 15px;
	width: 400px;
}
.btn-col .healcode-btn .squareButton {
	display: block;
}

.mfp-inline-holder .mfp-content {
	max-width: 500px;
}

.hc_registration input[type="text"],
.hc_registration input[type="password"],
.hc_registration input[type="email"] {
	height: 36px;
	line-height: 36px;
	padding: 0 15px;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	font-family: "Roboto", sans-serif;
	font-size: 14px;
	outline: none;
	color: #888888;
	background-color: #fafafa;
	border: 1px solid #ddd;
	margin: 0px;
	background-color: #fff;
	width: 100%;
}
.hc_registration select {
	background-color: #fafafa;
	border: 1px solid #ddd;
	color: #888888;
	font-family: "Roboto", sans-serif;
	font-size: 14px;
	height: 36px;
	line-height: 36px;
	width: 100%;
}
.hc_registration textarea {
	font-family: "Roboto", sans-serif;
	font-size: 14px;
	outline: none;
	color: #888888;
	background-color: #fafafa;
	border: 1px solid #ddd;
	margin: 0px;
	padding: 15px;
	resize: vertical;
	box-sizing: border-box;
	width: 100%;
}
.hc_registration #registrations_birth_date_month,
.hc_registration #registrations_birth_date_day,
.hc_registration #registrations_birth_date_year {
	width: auto;
}

/* HEALCODE Buttons */
.wpb_wrapper {
	font-family: inherit;
	font-size: inherit;
	font-weight: inherit;
	font-style: inherit;
}
.healcode-btn {
	text-align: center;
	width: 400px;
	margin-bottom: 15px !important;
	margin: auto;
	display: block;
	background-color: #006fba;
	border: 1px solid #069eed;
	transition: all 0.3s ease;
}
.healcode-btn:active {
	background-color: #323232;
	border-color: #323232;
}
.healcode-btn a,
.healcode-btn a:hover,
.healcode-btn a:focus {
	color: #fff;
}
.healcode-btn a {
	font-size: 18px;
	text-decoration: none;
	min-height: 40px;
	height: auto;
	line-height: 40px;
	padding: 10px 35px;
	width: 400px;
	overflow: hidden;
	outline: none;
}

@media (max-width: 568px) {
	.healcode-btn {
		width: auto;
		font-size: inherit;
	}
	.healcode-btn a {
		padding: 10px 20px;
	}
}
