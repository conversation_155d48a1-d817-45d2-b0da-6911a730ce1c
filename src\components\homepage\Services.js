import React from 'react';
import { Link } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import { useServicesHome } from '../../hooks/getServicesHome';

const Services = () => {
    const servicesData = useServicesHome();
    const services = servicesData.wpPage.acf.service_boxes_list;

    return (
        <section className="homethreeboxes page-section bg-primary text-white">
            <div className="container flexwrap">
                {services.map(service => (
                    <div className="hbox flexbox" key={service.service_title}>
                        <GatsbyImage
                            image={getImage(
                                service.service_image.localFile
                            )}
                            className="whiteborderimg"
                            alt={service.service_title || 'Service Image'}
                        />
                        <h3
                            dangerouslySetInnerHTML={{
                                __html: service.service_title,
                            }}
                        />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: service.service_description,
                            }}
                        />
                        <Link
                            to={service.service_link}
                            className="wrtsbtn yellowbtn"
                        >
                            MORE INFO
                        </Link>
                    </div>
                ))}
            </div>
        </section>
    );
};

export default Services;
