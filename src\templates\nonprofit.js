import React from 'react';
import { graphql } from 'gatsby';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
import MyBrotherRTS from '../images/mbrtsimg.jpg';
import SEO from '../components/seo';
import LineFull from '../images/linefull.jpg';
import '../styles/app.scss';

const NonProfit = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt">
                <div className="container">
                    <img
                        src={
                            post.featuredImage?.node?.localFile?.childImageSharp?.fluid?.src ||
                            MyBrotherRTS
                        }
                        alt="mbrtsimg"
                    />
                    <div
                        dangerouslySetInnerHTML={{
                            __html: post.content,
                        }}
                    />
                    <img
                        className="nonprofline"
                        src={LineFull}
                        alt="line full"
                    />
                    <a
                        href={post.acf?.mbrts_website_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="wrtsbtn yellowbtn biggerbtn"
                    >
                        VISIT THE MBRTS WEBSITE
                    </a>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default NonProfit;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            featuredImage {
                node {
                    localFile {
                        childImageSharp {
                            fluid {
                                srcWebp
                                src
                            }
                        }
                    }
                }
            }
            acf {
                mbrts_website_link
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;
