
// import React from "react"
// import { graphql } from "gatsby"

// const LocationsPage = ({ data }) => {
//     const locations = data.locations.nodes

//     return (
//         <div>
//             <h1>Locations</h1>
//             <ul>
//                 {locations.map(location => (
//                     <li key={location.id}>
//                         <h2>{location.title}</h2>
                     
//                         <p>{location.acf.gym_name}</p>
//                     </li>
//                 ))}
//             </ul>
//         </div>
//     )
// }

// export const query = graphql`
//   query {
//     locations: allCustomApiLocations {
//       nodes {
//         id
//         title
//         acf {
//           gym_name
//         }
//       }
//     }
//   }
// `

// export default LocationsPage;