import React from 'react';
import { useStaticQuery, graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';

/*
 * This component is built using `gatsby-image` to automatically serve optimized
 * images with lazy loading and reduced file sizes. The image is loaded using a
 * `useStaticQuery`, which allows us to load the image from directly within this
 * component, rather than having to pass the image data down from pages.
 *
 * For more information, see the docs:
 * - `gatsby-image`: https://gatsby.dev/gatsby-image
 * - `useStaticQuery`: https://www.gatsbyjs.org/docs/use-static-query/
 */

const Image = ({ imagePath = "gatsby-astronaut.png", altText = "Image" }) => {
    const data = useStaticQuery(graphql`
        query {
            allFile {
                nodes {
                    relativePath
                    childImageSharp {
                        gatsbyImageData(layout: CONSTRAINED, width: 300)
                    }
                }
            }
        }
    `);

    const imageNode = data.allFile.nodes.find(
        node => node.relativePath === imagePath
    );

    if (!imageNode) {
        console.error(`Image not found: ${imagePath}`);
        return <p>Image not found</p>; // Fallback if the image is missing
    }

    const image = getImage(imageNode.childImageSharp.gatsbyImageData);

    return <GatsbyImage image={image} alt={altText} />;
};

export default Image;
