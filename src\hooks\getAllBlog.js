import { useStaticQuery, graphql } from 'gatsby';

export const useAllBlog = () => {
    const AllPosts = useStaticQuery(
        graphql`
            query GET_All_POSTS {
                allWpPost(sort: {date: DESC}) {
                    nodes {
                        featuredImage {
                            node {
                                localFile {
                                    childImageSharp {
                                        gatsbyImageData(
                                            layout: CONSTRAINED
                                            width: 800
                                        )
                                    }
                                }
                            }
                        }
                        categories {
                            nodes {
                                name
                            }
                        }
                        date
                        excerpt
                        slug
                        title
                        id
                    }
                }
            }
        `
    );
    return AllPosts.allWpPost.nodes;
};
