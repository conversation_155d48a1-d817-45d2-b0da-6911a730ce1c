import React from 'react';
import { graphql } from 'gatsby';
import { GatsbyImage, getImage } from 'gatsby-plugin-image';
import HeaderMain from '../components/HeaderMain';
import Footer from '../components/Footer';
import HeroSingle from '../components/pages/HeroSingle';
// images
import LineFull from '../images/linefull.jpg';
import SEO from '../components/seo';
import '../styles/app.scss';

const SchoolsOut = ({ data }) => {
    const { wpPage: post } = data;

    const seoTitle = post.seo?.title || post.title;
    const seoDescription = post.seo?.metaDesc || '';

    return (
        <>
            <SEO
                title={seoTitle}
                description={seoDescription}
            />
            <HeaderMain />
            <HeroSingle pageTitle={post.title} />

            <section className="page-section smallestwdt centersec">
                <div className="container">
                    <div
                        dangerouslySetInnerHTML={{
                            __html: post.content,
                        }}
                    />
                    <a
                        href="https://clients.mindbodyonline.com/classic/home?studioid=44354"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="wrtsbtn yellowbtn inlinebtn"
                    >
                        {/* 
                        href={`tel:${post.acf.schools_out_call_number}`}
                        CALL {post.acf.schools_out_call_number} TO LEARN MORE
                        */}
                        Click here to sign up
                    </a>
                    <img
                        className="schoolsline"
                        src={LineFull}
                        alt="linefull"
                    />
                    <div
                        dangerouslySetInnerHTML={{
                            __html: post.acf.soSessionsText,
                        }}
                    />
                    {post.acf.soProgramImage?.localFile?.childImageSharp?.gatsbyImageData && (
                        <GatsbyImage
                            image={getImage(post.acf.soProgramImage.localFile.childImageSharp.gatsbyImageData)}
                            alt="Schools program"
                        />
                    )}
                    <img
                        className="schoolsline"
                        src={LineFull}
                        alt="linefull"
                    />
                    <h2 className="bluetxt">Check our pricing</h2>
                    <div className="smallestwdt packagesdiv">
                        {post.acf.soSessions.map((session, i) => (
                            <div
                                className="pricingbox schoolspricing bg-primary text-white"
                                key={i}
                            >
                                <h3>
                                    <span
                                        className="yellowtxt"
                                        dangerouslySetInnerHTML={{
                                            __html: session.sessionTitle,
                                        }}
                                    />
                                    <br />
                                    {session.sessionPricing}
                                </h3>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            <section className="whywelist page-section bg-secondary text-white">
                <div className="container flexwrap bdaytxtadjust">
                    <div className="whylistlft flexbox">
                        <h2
                            className="yellowtxt"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.soEnjoyTitle,
                            }}
                        />
                        <p
                            className="biggertxt"
                            dangerouslySetInnerHTML={{
                                __html: post.acf.soEnjoyContent,
                            }}
                        />

                        <h2 className="yellowtxt">
                            CALL {post.acf.schoolsOutCallNumber} TO LEARN
                            MORE
                        </h2>
                    </div>

                    <div className="flexbox">
                        <div className="equipstarimgrgt">
                            {post.acf.soEnjoyImage?.localFile?.childImageSharp?.gatsbyImageData && (
                                <GatsbyImage
                                    className="whiteborderimg"
                                    image={getImage(post.acf.soEnjoyImage.localFile.childImageSharp.gatsbyImageData)}
                                    alt="Unlimited opportunities"
                                />
                            )}
                        </div>
                    </div>
                </div>
            </section>
            <Footer />
        </>
    );
};

export default SchoolsOut;

export const pageQuery = graphql`
    query($id: String!) {
        wpPage(id: { eq: $id }) {
            id
            title
            slug
            content
            ... on WpACFSchoolsOut {
                schoolsOutCallNumber
                soSessionsText
                soEnjoyTitle
                soEnjoyContent
                soSessions {
                    sessionTitle
                    sessionPricing
                }
                soProgramImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 600
                                placeholder: BLURRED
                            )
                        }
                    }
                }
                soEnjoyImage {
                    localFile {
                        childImageSharp {
                            gatsbyImageData(
                                layout: CONSTRAINED
                                width: 600
                                placeholder: BLURRED
                            )
                        }
                    }
                }
            }
            seo {
                title
                metaDesc
            }
        }
    }
`;