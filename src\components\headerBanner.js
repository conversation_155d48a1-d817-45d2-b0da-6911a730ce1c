import React from 'react';
import { useHeaderInfo } from '../hooks/getHeaderInfo';

function BannerHead() {
    const data = useHeaderInfo();

    if (!data || !data.enable_header_banner) {
        return null; // Do not render the banner if it's not enabled or data is missing
    }

    return (
        <section
            className="header_banner is_active"
            dangerouslySetInnerHTML={{
                __html: data.header_banner_message_content || '',
            }}
        />
    );
}

export default BannerHead;
